#!/bin/bash
#
# data_download_ksi_mock.sh - Download KSI mock data from production server
#
# Description:
#   This script downloads the KSI tournament mock data from the production server
#   to the local development environment for testing purposes.
#
# Usage:
#   ./scripts/data_download_ksi_mock.sh
#
# Requirements:
#   - SSH access to the production server (<EMAIL>)
#   - The mock_data directory must exist on the production server
#
# Output:
#   Downloads tournament_matches.json to the local ./mock_data/ directory
#

echo "Downloading KSI mock data from production server..."

# Ensure local mock_data directory exists
mkdir -p mock_data

# Download the file using scp
scp <EMAIL>:/var/www/bragrights-be/mock_data/tournament_matches.json ./mock_data/tournament_matches.json

if [ $? -eq 0 ]; then
    echo "✅ Successfully downloaded tournament_matches.json"
    echo "File saved to: ./mock_data/tournament_matches.json"
else
    echo "❌ Failed to download the file"
    echo "Please ensure you have SSH access to the production server"
fi