#!/bin/bash
#
# maintenance_server_status.sh - BragRights Backend Status Checker
#
# Description:
#   This script performs a comprehensive check of all services and components
#   of the BragRights backend, including Puma, Sidekiq, Nginx, PostgreSQL,
#   Redis, API endpoints, and system resources.
#
# Usage:
#   ./scripts/maintenance_server_status.sh
#
# Output:
#   Displays the status of all services, API endpoints, and system resources.
#   Provides troubleshooting guidance for any issues detected.
#

echo "=================================="
echo "BragRights Backend Status Checker"
echo "=================================="
echo "Running checks at $(date)"
echo ""

# Check if <PERSON><PERSON> is running
echo "1. Checking Puma service status:"
if systemctl is-active --quiet puma; then
  echo "   ✅ Puma is running"
  systemctl status puma | grep "Active:"
else
  echo "   ❌ Puma is NOT running"
  systemctl status puma | grep "Active:"
  echo "   For detailed logs: sudo journalctl -u puma.service -n 20"
fi
echo ""

# Check if Sideki<PERSON> is running
echo "2. Checking Sidekiq service status:"
if systemctl is-active --quiet sidekiq; then
  echo "   ✅ Sidekiq is running"
  systemctl status sidekiq | grep "Active:"
else
  echo "   ❌ Sidekiq is NOT running"
  systemctl status sidekiq | grep "Active:"
  echo "   For detailed logs: sudo journalctl -u sidekiq.service -n 20"
fi
echo ""

# Check if Nginx is running
echo "3. Checking Nginx service status:"
if systemctl is-active --quiet nginx; then
  echo "   ✅ Nginx is running"
  systemctl status nginx | grep "Active:"
else
  echo "   ❌ Nginx is NOT running"
  systemctl status nginx | grep "Active:"
fi
echo ""

# Check if the API is responding
echo "4. Checking API health endpoints:"
# Check the root health endpoint
ROOT_API_URL="http://localhost/api/health"
ROOT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $ROOT_API_URL)

if [ "$ROOT_STATUS" == "200" ]; then
  echo "   ✅ Root API health endpoint is responding (HTTP 200)"
  echo "   Response: $(curl -s $ROOT_API_URL)"
else
  echo "   ❌ Root API health endpoint is NOT responding (HTTP $ROOT_STATUS)"
fi

# Check the v1 health endpoint
V1_API_URL="http://localhost/api/v1/health"
V1_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $V1_API_URL)

if [ "$V1_STATUS" == "200" ]; then
  echo "   ✅ V1 API health endpoint is responding (HTTP 200)"
  echo "   Response: $(curl -s $V1_API_URL)"
else
  echo "   ❌ V1 API health endpoint is NOT responding (HTTP $V1_STATUS)"
fi
echo ""

# Check PostgreSQL connection
echo "5. Checking PostgreSQL connection:"
if sudo -u postgres psql -c '\l' | grep bragrights_production > /dev/null; then
  echo "   ✅ PostgreSQL is running and database exists"
else
  echo "   ❌ PostgreSQL connection issue or database not found"
fi
echo ""

# Check Redis connection
echo "6. Checking Redis connection:"
if redis-cli ping | grep PONG > /dev/null; then
  echo "   ✅ Redis is running"
else
  echo "   ❌ Redis is not responding"
fi
echo ""

# Check system resources
echo "7. System Resources:"
echo "   RAM Usage:"
free -h | grep "Mem:"
echo ""
echo "   CPU Load:"
uptime
echo ""
echo "   Disk Usage:"
df -h / | grep -v "Filesystem"
echo ""

# Check recent logs
echo "8. Recent Rails logs:"
echo "   Last 5 lines from production.log:"
tail -5 /var/www/bragrights-be/log/production.log 2>/dev/null || echo "   No logs found or permission denied"
echo ""

# Check Nginx error logs
echo "9. Recent Nginx error logs:"
echo "   Last 5 lines from error.log:"
tail -5 /var/log/nginx/bragrights-be-error.log 2>/dev/null || echo "   No logs found or permission denied"
echo ""

echo "=================================="
echo "Public API Endpoint Check"
echo "=================================="

# Try to detect the domain name from Nginx config
DOMAIN=$(grep "server_name" /etc/nginx/sites-available/bragrights | awk '{print $2}' | tr -d ';')
if [ "$DOMAIN" == "_" ] || [ -z "$DOMAIN" ]; then
  # Use IP address if no domain is configured
  DOMAIN=$(curl -s http://checkip.amazonaws.com)
fi

echo "Checking public API endpoint at https://$DOMAIN/api/v1/health"
PUBLIC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -k https://$DOMAIN/api/v1/health)

if [ "$PUBLIC_STATUS" == "200" ]; then
  echo "✅ Public API is accessible (HTTP 200)"
else
  echo "❌ Public API is NOT accessible (HTTP $PUBLIC_STATUS)"
  echo "This could be due to:"
  echo "  - SSL not configured correctly"
  echo "  - Firewall blocking access"
  echo "  - The health endpoint doesn't exist in your Rails app"
fi

echo ""
echo "=================================="
echo "Status check complete"
echo "=================================="
echo "For more detailed logs:"
echo "- Puma: /var/www/bragrights-be/log/puma_*.log"
echo "- Rails: /var/www/bragrights-be/log/production.log"
echo "- Nginx: /var/log/nginx/bragrights-be-*.log"
echo "- System: journalctl -u puma.service -u sidekiq.service -u nginx.service"
echo ""
echo "To restart services if needed:"
echo "sudo systemctl restart puma"
echo "sudo systemctl restart sidekiq"
echo "sudo systemctl restart nginx"
echo ""

exit 0
