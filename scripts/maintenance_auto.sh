#!/bin/bash
#
# maintenance_auto.sh - Automated maintenance script for BragRights backend
#
# Description:
#   This script handles regular maintenance tasks that would otherwise need manual intervention.
#   It performs log cleanup, disk space monitoring, SSL certificate renewal, and service health checks.
#
# Usage:
#   ./scripts/maintenance_auto.sh
#
# Note:
#   This script is designed to run on the production server as a cron job.
#   Recommended schedule: Daily at off-peak hours
#


LOG_FILE="/home/<USER>/maintenance.log"

APP_PATH="/var/www/bragrights-be"

TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")


echo "[$TIMESTAMP] Starting automated maintenance tasks" > $LOG_FILE


# 1. Clean up old logs

echo "[$TIMESTAMP] Cleaning up old logs..." >> $LOG_FILE

find $APP_PATH/log -name "*.gz" -mtime +7 -delete


# 2. Clean up temporary files

echo "[$TIMESTAMP] Cleaning up temporary files..." >> $LOG_FILE

find $APP_PATH/tmp/cache -type f -mtime +7 -delete


# 3. Monitor disk space

DISK_USAGE=$(df / | grep / | awk '{ print $5}' | sed 's/%//g')

echo "[$TIMESTAMP] Current disk usage: $DISK_USAGE%" >> $LOG_FILE


if [ "$DISK_USAGE" -gt 80 ]; then

  echo "[$TIMESTAMP] WARNING: High disk usage detected ($DISK_USAGE%)" >> $LOG_FILE



  # Optional: Add notification command here (e.g., email alert)

  # mail -s "BragRights Server: High Disk Usage Alert" <EMAIL> < $LOG_FILE

fi


# 4. Update SSL certificates

echo "[$TIMESTAMP] Checking SSL certificates..." >> $LOG_FILE

sudo certbot renew --quiet


# 5. Check and restart services if needed

echo "[$TIMESTAMP] Checking services..." >> $LOG_FILE


systemctl is-active --quiet puma || {

  echo "[$TIMESTAMP] Puma is DOWN - restarting" >> $LOG_FILE

  sudo systemctl restart puma

}


systemctl is-active --quiet sidekiq || {

  echo "[$TIMESTAMP] Sidekiq is DOWN - restarting" >> $LOG_FILE

  sudo systemctl restart sidekiq

}


systemctl is-active --quiet nginx || {

  echo "[$TIMESTAMP] Nginx is DOWN - restarting" >> $LOG_FILE

  sudo systemctl restart nginx

}


# 6. Keep only last 10 maintenance logs

echo "[$TIMESTAMP] Rotating maintenance logs..." >> $LOG_FILE

MAINTENANCE_LOGS_DIR=$(dirname "$LOG_FILE")

ls -t $MAINTENANCE_LOGS_DIR/maintenance*.log 2>/dev/null | tail -n +11 | xargs -r rm


echo "[$TIMESTAMP] Maintenance tasks completed" >> $LOG_FILE


# Create a copy with timestamp for historical records

cp $LOG_FILE "${LOG_FILE%.log}_$TIMESTAMP.log"


exit 0

