#!/usr/bin/env ruby
#
# data_fetch_ksi.rb - Fetch KSI tournament data via SOAP API
#
# Description:
#   This script fetches tournament match data from the KSI SOAP API
#   and saves it to a JSON file for use in development and testing.
#
# Usage:
#   bundle exec rails runner scripts/data_fetch_ksi.rb
#
# Output:
#   Creates or updates mock_data/tournament_matches.json with the fetched data
#
# Requirements:
#   - Rails environment must be loaded
#   - KsiSoapService must be configured with proper credentials
#

require_relative '../config/environment'

# Initialize the KsiSoapService
ksi_service = KsiSoapService.new

# Fetch data from KSI
begin
  tournament_number = 49_315 # Your tournament number

  puts 'Fetching tournament matches...'
  response = ksi_service.client.call(:mot_leikir, message: { 'MotNumer' => tournament_number.to_s })

  # Extract the raw match data from the SOAP response
  matches_data = response.body[:mot_leikir_response][:mot_leikir_svar][:array_mot_leikir]

  # Structure the data in the same format as expected by the application
  final_data = {
    mot_leikur: matches_data
  }

  # Save to JSON file
  output_path = Rails.root.join('mock_data', 'tournament_matches.json')
  File.write(output_path, JSON.pretty_generate(final_data))
  puts "Tournament matches saved to #{output_path}"
  puts "Total matches fetched: #{matches_data.length}"
rescue Savon::SOAPFault => e
  puts "SOAP Fault: #{e.message}"
rescue Savon::HTTPError => e
  puts "HTTP Error: #{e.message}"
rescue StandardError => e
  puts "Unexpected Error: #{e.message}"
  puts e.backtrace.join("\n")
end
