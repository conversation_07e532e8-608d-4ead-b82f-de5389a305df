#!/bin/bash

echo "Verifying HTTPS API endpoints..."

# Domain is hardcoded here
DOMAIN="api.bragrights.football"

# Test the connection to both health endpoints
echo "Testing API health endpoints over HTTPS..."
echo "1. Testing /api/health endpoint:"
curl -v "https://$DOMAIN/api/health"
echo ""

echo "2. Testing /api/v1/health endpoint:"
curl -v "https://$DOMAIN/api/v1/health"
echo ""

# Check if Puma socket exists and has correct permissions
echo "Checking Puma socket..."
SOCKET_PATH="/var/www/bragrights-be/tmp/sockets/puma.sock"
if [ -S "$SOCKET_PATH" ]; then
  echo "✅ Socket exists"
  ls -la "$SOCKET_PATH"
else
  echo "❌ Socket does not exist!"
  echo "Checking if <PERSON><PERSON> is running:"
  systemctl status puma --no-pager || true
fi

# Check Nginx configuration
echo "Checking Nginx configuration..."
grep -n "location /api" /etc/nginx/sites-available/bragrights || echo "No /api location found in Nginx config"

echo ""
echo "If endpoints are still not working, try the following:"
echo "1. Restart services: sudo systemctl restart puma nginx"
echo "2. Make sure your DNS is pointing correctly to your server"
echo "3. Check that your API controllers exist and have the correct routes"
echo "4. Verify SSL certificate: sudo certbot certificates"

exit 0
