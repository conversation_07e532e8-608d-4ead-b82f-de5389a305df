#!/bin/bash

set -e

echo "Fixing Puma socket connection issues..."
APP_PATH="/var/www/bragrights-be"

# 1. Check if <PERSON>uma is creating a socket file
echo "Checking if Puma socket exists..."
ps aux | grep puma | grep -v grep

# Find the actual socket path being used
echo "Current socket path in Puma process:"
ACTUAL_SOCKET=$(ps aux | grep puma | grep -v grep | grep -o "unix://[^'\" )]*" | head -1)

if [ -z "$ACTUAL_SOCKET" ]; then
  echo "❌ Could not find socket path in Puma process"
  SOCKET_PATH="/var/www/bragrights-be/tmp/sockets/puma.sock"
  echo "Will use default path: $SOCKET_PATH"
else
  SOCKET_PATH=${ACTUAL_SOCKET#unix://}
  echo "✅ Found socket: $SOCKET_PATH"
fi

# 2. Check Nginx configuration
echo "Checking Nginx configuration..."
NGINX_SOCKET=$(grep -r "unix://" /etc/nginx/sites-available/bragrights | head -1)
echo "Current Nginx socket config: $NGINX_SOCKET"

# Fix the Nginx configuration (remove any trailing parenthesis)
echo "Updating Nginx configuration..."
sudo sed -i 's|unix:///var/www/bragrights-be/tmp/sockets/puma.sock))|unix:///var/www/bragrights-be/tmp/sockets/puma.sock|g' /etc/nginx/sites-available/bragrights
sudo sed -i 's|unix:///var/www/bragrights-be/tmp/sockets/puma.sock)|unix:///var/www/bragrights-be/tmp/sockets/puma.sock|g' /etc/nginx/sites-available/bragrights

# Ensure socket directory exists with proper permissions
echo "Ensuring socket directory exists with correct permissions..."
mkdir -p "$APP_PATH/tmp/sockets"
chmod 755 "$APP_PATH/tmp"
chmod 755 "$APP_PATH/tmp/sockets"

# Update the Puma configuration to ensure it creates the socket
echo "Updating Puma configuration..."
cat > $APP_PATH/config/puma_production.rb <<EOL
#!/usr/bin/env puma

# Basic configuration
directory '$APP_PATH'
environment 'production'
pidfile '$APP_PATH/tmp/pids/puma.pid'
state_path '$APP_PATH/tmp/pids/puma.state'
stdout_redirect '$APP_PATH/log/puma_access.log', '$APP_PATH/log/puma_error.log', true

# Threads configuration - Low Memory Setup
threads 1, 3

# Workers configuration - Only 1 worker for low memory usage
workers 1

# Socket for Nginx - explicit path
bind 'unix://$APP_PATH/tmp/sockets/puma.sock'

# Preload app for better performance
preload_app!

on_worker_boot do
  ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
end

plugin :tmp_restart
EOL

# Verify Nginx configuration
sudo nginx -t

# Restart services
echo "Restarting Puma and Nginx..."
sudo systemctl restart puma
sudo systemctl restart nginx

echo "Waiting for services to start..."
sleep 3

# Check if socket file exists now
ls -la "$APP_PATH/tmp/sockets/"

echo "Testing API endpoints..."
curl -v http://localhost/api/health
curl -v http://localhost/api/v1/health

echo "Done! Socket connection should now be fixed."
exit 0
