#!/bin/bash

APP_PATH="/var/www/bragrights-be"
cd $APP_PATH

# Try to start Puma directly without Rails
echo "Attempting to start Puma with minimal configuration..."
echo "This will help identify the exact error..."

# First, try a super basic Puma configuration
cat > $APP_PATH/config/puma_super_minimal.rb <<EOL
workers 0
threads 1, 1
port 3000
EOL

# Try to start Puma with this minimal config
echo "Starting Puma with super minimal config..."
RAILS_ENV=production bundle exec puma -C config/puma_super_minimal.rb --debug

exit 0
