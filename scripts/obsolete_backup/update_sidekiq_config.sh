#!/bin/bash
# update_sidekiq_config.sh
# Script to deploy Sidekiq configuration to production server

echo "Deploying updated Sidekiq configuration to production server"

# Copy the initializer and service file to the production server
echo "Copying Sidekiq initializer and service file..."
scp config/initializers/sidekiq.rb <EMAIL>:/var/www/bragrights-be/config/initializers/
scp config/sidekiq.service <EMAIL>:/var/www/bragrights-be/config/

# Copy the jobs
echo "Copying job files..."
scp app/jobs/daily_match_update_job.rb <EMAIL>:/var/www/bragrights-be/app/jobs/

echo "Files have been copied to your production server."
echo "To complete the installation, please run the following commands on your production server:"
echo ""
echo "  cd /var/www/bragrights-be"
echo "  sudo cp config/sidekiq.service /etc/systemd/system/sidekiq.service"
echo "  sudo systemctl daemon-reload"
echo "  sudo systemctl restart sidekiq"
echo "  sudo systemctl status sidekiq"
echo "  tail -n 20 /var/log/sidekiq.log"
echo ""
echo "You can SSH into your server with: ssh <EMAIL>"