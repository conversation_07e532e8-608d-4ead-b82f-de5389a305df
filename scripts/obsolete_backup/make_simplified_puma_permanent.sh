#!/bin/bash

set -e

echo "Making simplified Puma configuration permanent..."

APP_PATH="/var/www/bragrights-be"
USER=$(whoami)
RUBY_PATH=$(which ruby)
BUNDLE_PATH=$(which bundle)

echo "Using Ruby at: $RUBY_PATH"
echo "Using Bundler at: $BUNDLE_PATH"

# Create a simple but complete Puma config
echo "Creating optimized Puma configuration..."
cat > $APP_PATH/config/puma_production.rb <<EOL
#!/usr/bin/env puma

# Basic configuration
directory '$APP_PATH'
environment 'production'
pidfile '$APP_PATH/tmp/pids/puma.pid'
state_path '$APP_PATH/tmp/pids/puma.state'

# Logging
stdout_redirect '$APP_PATH/log/puma_access.log', '$APP_PATH/log/puma_error.log', true

# Threads and workers
threads 1, 3
workers 1

# Socket for Nginx
bind 'unix://$APP_PATH/tmp/sockets/puma.sock'

# Preload app for better performance
preload_app!

on_worker_boot do
  ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
end
EOL

# Create permanent simple service file
echo "Creating permanent Puma service..."
sudo tee /etc/systemd/system/puma.service > /dev/null <<EOL
[Unit]
Description=Puma HTTP Server
After=network.target postgresql.service redis-server.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=production"
EnvironmentFile=$APP_PATH/.env
ExecStart=$BUNDLE_PATH exec puma -C $APP_PATH/config/puma_production.rb
ExecReload=/bin/kill -s USR1 \$MAINPID
TimeoutSec=300
Restart=always
RestartSec=10

# Memory optimization
MemoryAccounting=true
MemoryLimit=512M

[Install]
WantedBy=multi-user.target
EOL

sudo systemctl daemon-reload
echo "Service configuration updated permanently"

# Ensure correct directories exist with proper permissions
mkdir -p $APP_PATH/tmp/pids
mkdir -p $APP_PATH/tmp/sockets
chmod -R 755 $APP_PATH/tmp
chmod -R 755 $APP_PATH/log

# Restart service with new configuration
echo "Restarting Puma with permanent configuration..."
sudo systemctl restart puma
sleep 3

# Check if service started successfully
if systemctl is-active --quiet puma; then
  echo "✅ Puma service started successfully with permanent configuration!"
  
  # Check socket creation
  if [ -e "$APP_PATH/tmp/sockets/puma.sock" ]; then
    echo "✅ Puma socket created successfully!"
  else
    echo "⚠️ Puma socket not found. Nginx may not be able to connect."
  fi
  
  # Create a check symlink to verify proper operation
  echo "#!/bin/bash" > /tmp/check-puma.sh
  echo "systemctl status puma" >> /tmp/check-puma.sh
  echo "ls -la $APP_PATH/tmp/sockets/" >> /tmp/check-puma.sh
  chmod +x /tmp/check-puma.sh
  ln -sf /tmp/check-puma.sh $HOME/check-puma
  
  echo ""
  echo "✅ SUCCESS! Your Puma server is now running with a permanent configuration."
  echo "You can check its status anytime with: $HOME/check-puma"
else
  echo "❌ Puma service failed to start with permanent configuration."
  echo "Check logs with: sudo journalctl -u puma -n 50"
fi

exit 0
