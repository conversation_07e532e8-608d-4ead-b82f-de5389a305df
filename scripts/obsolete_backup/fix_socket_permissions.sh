#!/bin/bash

set -e

echo "Fixing socket permission issues between Nginx and Puma..."
APP_PATH="/var/www/bragrights-be"
SOCKET_PATH="$APP_PATH/tmp/sockets/puma.sock"

# Check which users are running the services
echo "Checking service users:"
NGINX_USER=$(ps -ef | grep nginx | grep master | head -1 | awk '{print $1}')
PUMA_USER=$(ps -ef | grep puma | grep -v grep | head -1 | awk '{print $1}')
echo "Nginx is running as: $NGINX_USER"
echo "Puma is running as: $PUMA_USER"

# Check socket permissions
echo "Current socket permissions:"
ls -la $SOCKET_PATH || echo "Socket file doesn't exist yet"

# Fix socket directory and create a .socket_fix marker file
echo "Creating socket directory with correct permissions..."
mkdir -p $APP_PATH/tmp/sockets
sudo chmod -R 777 $APP_PATH/tmp
touch $APP_PATH/tmp/sockets/.socket_fix

# Update the Puma configuration to use the right permissions
echo "Updating Puma configuration..."
cat > $APP_PATH/config/puma_production.rb <<EOL
#!/usr/bin/env puma

# Basic configuration
directory '$APP_PATH'
environment 'production'
pidfile '$APP_PATH/tmp/pids/puma.pid'
state_path '$APP_PATH/tmp/pids/puma.state'
stdout_redirect '$APP_PATH/log/puma_access.log', '$APP_PATH/log/puma_error.log', true

# Threads configuration - Low Memory Setup
threads 1, 3

# Workers configuration - Only 1 worker for low memory usage
workers 1

# Socket for Nginx with umask for world-readable permissions
# This is key to fixing the permission issue
umask 0000
bind 'unix://$APP_PATH/tmp/sockets/puma.sock'

# Preload app for better performance
preload_app!

on_worker_boot do
  ActiveRecord::Base.establish_connection if defined?(ActiveRecord)
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect! if defined?(ActiveRecord)
end

plugin :tmp_restart
EOL

# Make sure Nginx is in the same group or set appropriate permissions
echo "Making sure Nginx and www-data user can access the socket..."
sudo usermod -a -G $PUMA_USER www-data || echo "Failed to add www-data to $PUMA_USER group"
sudo usermod -a -G $PUMA_USER $NGINX_USER || echo "Failed to add $NGINX_USER to $PUMA_USER group"

# Restart services
echo "Restarting services..."
sudo systemctl restart puma
sleep 3
sudo systemctl restart nginx

echo "Waiting for socket to be created..."
sleep 3

# Check if socket was created with proper permissions
if [ -e "$SOCKET_PATH" ]; then
  echo "Socket file created:"
  ls -la $SOCKET_PATH
  sudo chmod 777 $SOCKET_PATH
  echo "Changed socket permissions to 777 (world-readable)"
else
  echo "Socket file not created yet!"
fi

echo "Testing API health endpoints..."
curl -v http://localhost/api/health
echo ""
curl -v http://localhost/api/v1/health

echo "Permission fix complete. If issues persist, you may need to manually adjust permissions."
exit 0
