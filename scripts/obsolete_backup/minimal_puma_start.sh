#!/bin/bash

set -e

APP_PATH="/var/www/bragrights-be"
USER=$(whoami)
BUNDLE_PATH=$(which bundle)

echo "Creating absolutely minimal Puma configuration..."

# Create an ultra-minimal config
cat > $APP_PATH/config/puma_minimal.rb <<EOL
#!/usr/bin/env puma

directory '$APP_PATH'
environment 'production'
threads 1, 1
workers 0
bind 'unix://$APP_PATH/tmp/sockets/puma.sock'
EOL

# Create a minimal service file
sudo tee /etc/systemd/system/puma.service > /dev/null <<EOL
[Unit]
Description=Puma HTTP Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_PATH
Environment="RAILS_ENV=production"
EnvironmentFile=$APP_PATH/.env
ExecStart=$BUNDLE_PATH exec puma -e production -b 'unix://$APP_PATH/tmp/sockets/puma.sock'
Restart=always

[Install]
WantedBy=multi-user.target
EOL

sudo systemctl daemon-reload
echo "Service configuration updated with minimal settings"

# Ensure correct directories exist with proper permissions
mkdir -p $APP_PATH/tmp/pids
mkdir -p $APP_PATH/tmp/sockets
chmod -R 755 $APP_PATH/tmp

echo "Attempting to start Puma with minimal configuration..."
sudo systemctl restart puma
sleep 3

# Check if service started
if systemctl is-active --quiet puma; then
  echo "✅ Puma service started successfully with minimal configuration!"
else
  echo "❌ Puma service still failing. Running direct command to see errors:"
  cd $APP_PATH
  RAILS_ENV=production $BUNDLE_PATH exec puma -b "unix://$APP_PATH/tmp/sockets/puma.sock"
fi

exit 0
