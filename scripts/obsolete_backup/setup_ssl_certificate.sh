#!/bin/bash

set -e

echo "Setting up SSL certification for api.bragrights.football..."

# Check if domain is correctly configured in Nginx
DOMAIN="api.bragrights.football"
NGINX_CONFIG="/etc/nginx/sites-available/bragrights"

if ! grep -q "$DOMAIN" "$NGINX_CONFIG"; then
  echo "Error: Domain $DOMAIN not found in Nginx configuration."
  echo "Please run './scripts/setup_api_subdomain.sh' first."
  exit 1
fi

# Make sure certbot is installed
if ! command -v certbot &> /dev/null; then
  echo "Installing Certbot..."
  sudo apt-get update
  sudo apt-get install -y certbot python3-certbot-nginx
fi

# Get SSL certificate using Certbot
echo "Obtaining SSL certificate from Let's Encrypt..."
sudo certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>

# Check if certificate was installed correctly
if [ $? -eq 0 ]; then
  echo "✅ SSL certificate successfully installed!"
  
  # Test Nginx configuration
  sudo nginx -t && sudo systemctl reload nginx
  
  echo ""
  echo "Testing HTTPS connection..."
  curl -sI https://$DOMAIN | head -n 1 || echo "Could not connect to HTTPS endpoint"
else
  echo "❌ Failed to obtain SSL certificate. Check errors above."
fi

# Verify firewall settings
echo ""
echo "Checking firewall settings..."
sudo ufw status | grep "443"

if ! sudo ufw status | grep -q "443.*ALLOW"; then
  echo "Adding firewall rule for HTTPS (port 443)..."
  sudo ufw allow 'Nginx Full'
  sudo ufw reload
fi

echo ""
echo "SSL setup complete. Your API should now be accessible via HTTPS."
echo "You can test it with: curl -v https://$DOMAIN/api/v1/health"
echo ""
echo "If you're still having issues, check:"
echo "1. DNS settings for $DOMAIN (must point to this server's IP)"
echo "2. Internet connectivity from this server"
echo "3. Any firewalls between clients and this server"

exit 0
