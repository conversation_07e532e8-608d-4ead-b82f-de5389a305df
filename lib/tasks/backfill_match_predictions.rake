# lib/tasks/backfill_match_predictions.rake
namespace :predictions do
  desc 'Backfill points for all finished matches'
  task backfill_points: :environment do
    puts 'Starting backfill of match prediction points...'

    finished_matches = Match.where(status: 'FINISHED')
    total_matches = finished_matches.count

    puts "Found #{total_matches} finished matches"

    progress = 0
    updated_predictions = 0

    finished_matches.find_each do |match|
      match.match_predictions.each do |prediction|
        prediction.calculate_points
        updated_predictions += 1
      end

      progress += 1
      if progress % 10 == 0
        puts "Progress: #{progress}/#{total_matches} matches processed, #{updated_predictions} predictions updated"
      end
    end

    puts "✅ Backfill complete! Updated #{updated_predictions} predictions across #{total_matches} matches"
  end

  desc 'Recalculate points for a specific match'
  task :recalculate_match, [:match_id] => :environment do |t, args|
    match = Match.find(args[:match_id])

    puts "⚠️  Warning: Match #{match.id} is not finished (status: #{match.status})" if match.status != 'FINISHED'

    count = 0
    match.match_predictions.each do |prediction|
      prediction.calculate_points
      count += 1
    end

    puts "✅ Recalculated points for #{count} predictions on match #{match.id}"
  end

  desc 'Recalculate points for all predictions for a specific season'
  task :recalculate_season, [:season_id] => :environment do |t, args|
    season = Season.find(args[:season_id])
    matches = Match.where(season:, status: 'FINISHED')

    puts "Recalculating points for #{matches.count} finished matches in season #{season.id}..."

    total_predictions = 0
    matches.find_each do |match|
      match.match_predictions.each do |prediction|
        prediction.calculate_points
        total_predictions += 1
      end
    end

    puts "✅ Recalculated #{total_predictions} predictions for season #{season.id}"
  end
end
