# app/serializers/api/v1/admin/user_serializer.rb
module Api
  module V1
    module Admin
      class UserSerializer < ActiveModel::Serializer
        attributes :id, :email, :username, :admin,
                   :is_content_creator,
                   :youtube_channel_id,
                   :youtube_channel_name,
                   :youtube_subscriber_count,
                   :youtube_avatar_url,
                   :created_at, :updated_at
      end
    end
  end
end

