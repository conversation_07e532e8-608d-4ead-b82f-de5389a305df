# app/serializers/api/v1/admin/league_serializer.rb
module Api
  module V1
    module Admin
      class LeagueSerializer < ActiveModel::Serializer
        attributes :id,
                   :name,
                   :open,
                   :archived,
                   :archived_at,
                   :starting_matchday,
                   :subscriber_only,
                   :youtube_league,
                   :youtube_channel_id,
                   :member_count,
                   :created_at,
                   :updated_at

        belongs_to :owner, serializer: Api::V1::Admin::UserSerializer

        attribute :competition do
          comp = object.try(:competition) || Competition.find_by(id: object.competition_id)
          comp&.slice(:id, :name, :code)
        end

        attribute :season do
          season = object.try(:season) || Season.find_by(id: object.season_id)
          season&.slice(:id, :start_date, :end_date)
        end

        def member_count
          object.memberships.size
        end
      end
    end
  end
end

