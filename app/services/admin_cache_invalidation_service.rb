class AdminCacheInvalidationService
  include ActiveModel::Model

  CACHE_PATTERNS = {
    competition: [
      'competition_%<id>s',
      'competition_%<code>s',
      'predictable_matches_%<code>s',
      'competition_teams_%<id>s',
      'competition_seasons_%<id>s'
    ],
    season: [
      'season_%<id>s',
      'season_matches_%<id>s',
      'season_teams_%<id>s',
      'predictable_matches_%<competition_code>s',
      'league_standings_%<id>s'
    ],
    match: [
      'match_%<id>s',
      'predictable_matches_%<competition_code>s',
      'season_matches_%<season_id>s',
      'matchday_%<season_id>s_%<matchday>s',
      'league_standings_%<season_id>s'
    ],
    team: [
      'team_%<id>s',
      'competition_teams_%<competition_id>s',
      'season_teams_%<season_id>s'
    ],
    league: [
      'league_%<id>s',
      'user_leagues_%<user_id>s',
      'league_standings_%<id>s',
      'league_predictions_%<id>s'
    ]
  }.freeze

  def self.invalidate_for(resource_type, resource, additional_context = {})
    new(resource_type, resource, additional_context).invalidate
  end

  def initialize(resource_type, resource, additional_context = {})
    @resource_type = resource_type.to_sym
    @resource = resource
    @additional_context = additional_context
  end

  def invalidate
    return unless CACHE_PATTERNS.key?(@resource_type)

    patterns = CACHE_PATTERNS[@resource_type]
    context = build_context

    patterns.each do |pattern|
      cache_key_pattern = pattern % context
      invalidate_pattern(cache_key_pattern)
    end

    # Invalidate related resources
    invalidate_related_resources

    log_invalidation(patterns, context)
  end

  private

  def build_context
    context = @additional_context.dup

    case @resource_type
    when :competition
      context.merge!(
        id: @resource.id,
        code: @resource.code
      )
    when :season
      context.merge!(
        id: @resource.id,
        competition_code: @resource.competition.code
      )
    when :match
      context.merge!(
        id: @resource.id,
        season_id: @resource.season_id,
        matchday: @resource.matchday,
        competition_code: @resource.season.competition.code
      )
    when :team
      context.merge!(
        id: @resource.id,
        competition_id: @additional_context[:competition_id],
        season_id: @additional_context[:season_id]
      )
    when :league
      context.merge!(
        id: @resource.id,
        user_id: @resource.creator_id
      )
    end

    context
  end

  def invalidate_pattern(pattern)
    if pattern.include?('*')
      # Use delete_matched for wildcard patterns
      Rails.cache.delete_matched(pattern)
    else
      # Direct cache key deletion
      Rails.cache.delete(pattern)
    end
  end

  def invalidate_related_resources
    case @resource_type
    when :competition
      # Invalidate all seasons for this competition
      @resource.seasons.find_each do |season|
        self.class.invalidate_for(:season, season)
      end
    when :season
      # Invalidate competition cache
      self.class.invalidate_for(:competition, @resource.competition)

      # Invalidate all matches for this season
      Rails.cache.delete_matched("match_*_season_#{@resource.id}")
    when :match
      # Invalidate season and competition caches
      self.class.invalidate_for(:season, @resource.season)

      # Invalidate predictions for this match
      Rails.cache.delete_matched("predictions_match_#{@resource.id}")
    end
  end

  def log_invalidation(patterns, context)
    Rails.logger.info "Cache invalidation for #{@resource_type} #{@resource.id}: #{patterns.map do |p|
                                                                                     p % context
                                                                                   end.join(', ')}"
  end
end
