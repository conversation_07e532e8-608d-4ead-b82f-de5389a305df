# app/services/admin/league_ownership_transfer_service.rb
module Admin
  class LeagueOwnershipTransferService
    attr_reader :league, :new_owner, :performed_by, :errors

    def initialize(league:, new_owner:, performed_by:)
      @league = league
      @new_owner = new_owner
      @performed_by = performed_by
      @errors = []
    end

    def call
      return error!('New owner is the same as current owner') if league.owner_id == new_owner.id

      League.transaction do
        old_owner_id = league.owner_id
        league.update!(owner_id: new_owner.id)
        # Ensure new owner is a member
        league.memberships.find_or_create_by!(user_id: new_owner.id)
        Rails.logger.info("[AUDIT] Admin #{performed_by&.id} transferred league #{league.id} ownership from #{old_owner_id} to #{new_owner.id}")
      end

      true
    rescue StandardError => e
      error!(e.message)
      false
    end

    private

    def error!(msg)
      @errors << msg
      Rails.logger.warn("[AUDIT] League ownership transfer failed for league #{league&.id}: #{msg}")
    end
  end
end

