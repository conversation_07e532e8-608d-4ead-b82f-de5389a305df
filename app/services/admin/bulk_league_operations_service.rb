# app/services/admin/bulk_league_operations_service.rb
module Admin
  class BulkLeagueOperationsService
    SUPPORTED_ACTIONS = %w[archive unarchive].freeze

    def initialize(ids:, action_name:, params:, performed_by:)
      @ids = Array(ids)
      @action_name = action_name.to_s
      @params = params
      @performed_by = performed_by
    end

    def call
      return { errors: ['Unsupported action'], action: @action_name } unless SUPPORTED_ACTIONS.include?(@action_name)

      leagues = League.where(id: @ids)
      processed = []
      errors = []

      League.transaction do
        leagues.find_each do |league|
          case @action_name
          when 'archive'
            league.archive!
            Rails.logger.info("[AUDIT] Admin #{@performed_by&.id} archived league #{league.id} (bulk)")
            processed << { id: league.id, status: 'archived' }
          when 'unarchive'
            league.unarchive!
            Rails.logger.info("[AUDIT] Admin #{@performed_by&.id} unarchived league #{league.id} (bulk)")
            processed << { id: league.id, status: 'unarchived' }
          end
        rescue StandardError => e
          errors << { id: league.id, error: e.message }
        end
      end

      { action: @action_name, processed:, errors: }
    end
  end
end

