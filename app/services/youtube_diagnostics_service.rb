# app/services/youtube_diagnostics_service.rb
# Service for diagnosing YouTube API and channel issues

class YoutubeDiagnosticsService
  def initialize(user)
    @user = user
    @service = Google::Apis::YoutubeV3::YouTubeService.new
    @service.key = ENV.fetch('YOUTUBE_API_KEY', nil)
  end

  def diagnose_channel_issues
    Rails.logger.info "Starting YouTube channel diagnostics for user: #{@user.id}"

    diagnostics = {
      user_id: @user.id,
      timestamp: Time.current,
      checks: {},
      recommendations: []
    }

    # Check 1: Basic connection status
    diagnostics[:checks][:credentials_present] = @user.youtube_credentials.present?

    # Check 2: Parse credentials
    if diagnostics[:checks][:credentials_present]
      begin
        credentials = JSON.parse(@user.youtube_credentials)
        diagnostics[:checks][:credentials_valid_json] = true
        diagnostics[:checks][:access_token_present] = credentials['access_token'].present?
        diagnostics[:checks][:refresh_token_present] = credentials['refresh_token'].present?
        diagnostics[:checks][:token_expiry] = Time.at(credentials['expires_at'].to_i) if credentials['expires_at']
        diagnostics[:checks][:scopes] = credentials['scope']&.split || []

        # Check if token is expired
        if credentials['expires_at']
          diagnostics[:checks][:token_expired] = Time.now.to_i >= credentials['expires_at'].to_i
        end
      rescue JSON::ParserError => e
        diagnostics[:checks][:credentials_valid_json] = false
        diagnostics[:checks][:parse_error] = e.message
        diagnostics[:recommendations] << 'Credentials are corrupted. User should reconnect YouTube account.'
        return diagnostics
      end
    else
      diagnostics[:recommendations] << 'No YouTube credentials found. User needs to connect YouTube account.'
      return diagnostics
    end

    # Check 3: Token refresh if needed
    access_token = YoutubeTokenService.ensure_fresh_token(@user)
    diagnostics[:checks][:fresh_token_available] = access_token.present?

    if access_token.blank?
      diagnostics[:recommendations] << 'Could not obtain fresh access token. User may need to re-authorize.'
      return diagnostics
    end

    # Check 4: Test API connection with user token
    begin
      @service.authorization = access_token

      # Test basic API connection
      diagnostics[:checks][:api_connection] = test_api_connection

      # Check for channels using 'mine: true'
      channels_mine = @service.list_channels('snippet,statistics', mine: true)
      diagnostics[:checks][:channels_mine_count] = channels_mine.items&.count || 0

      if diagnostics[:checks][:channels_mine_count] > 0
        channel = channels_mine.items.first
        diagnostics[:checks][:primary_channel] = {
          id: channel.id,
          title: channel.snippet.title,
          subscriber_count: channel.statistics&.subscriber_count,
          created_at: channel.snippet.published_at
        }
      end

      # Check for managed channels (for brand accounts)
      begin
        managed_channels = @service.list_channels('snippet,statistics', managed_by_me: true)
        diagnostics[:checks][:managed_channels_count] = managed_channels.items&.count || 0

        if diagnostics[:checks][:managed_channels_count] > 0
          diagnostics[:checks][:managed_channels] = managed_channels.items.map do |channel|
            {
              id: channel.id,
              title: channel.snippet.title,
              subscriber_count: channel.statistics&.subscriber_count
            }
          end
        end
      rescue Google::Apis::Error => e
        diagnostics[:checks][:managed_channels_error] = e.message
      end
    rescue Google::Apis::AuthorizationError => e
      diagnostics[:checks][:api_auth_error] = e.message
      diagnostics[:recommendations] << 'Authorization failed. User needs to re-authorize with YouTube.'
    rescue Google::Apis::Error => e
      diagnostics[:checks][:api_error] = e.message
      diagnostics[:recommendations] << "YouTube API error: #{e.message}"
    end

    # Generate recommendations based on findings
    generate_recommendations(diagnostics)

    Rails.logger.info "YouTube diagnostics completed for user #{@user.id}: #{diagnostics[:checks].keys.join(', ')}"
    diagnostics
  end

  def test_channel_retrieval_methods
    Rails.logger.info "Testing different channel retrieval methods for user: #{@user.id}"

    access_token = YoutubeTokenService.ensure_fresh_token(@user)
    return { error: 'No access token available' } if access_token.blank?

    @service.authorization = access_token
    results = {}

    # Method 1: mine: true
    begin
      channels = @service.list_channels('snippet,statistics', mine: true)
      results[:mine_true] = {
        count: channels.items&.count || 0,
        channels: channels.items&.map { |c| { id: c.id, title: c.snippet.title } } || []
      }
    rescue Google::Apis::Error => e
      results[:mine_true] = { error: e.message }
    end

    # Method 2: managedByMe: true
    begin
      channels = @service.list_channels('snippet,statistics', managed_by_me: true)
      results[:managed_by_me] = {
        count: channels.items&.count || 0,
        channels: channels.items&.map { |c| { id: c.id, title: c.snippet.title } } || []
      }
    rescue Google::Apis::Error => e
      results[:managed_by_me] = { error: e.message }
    end

    # Method 3: Get user info and try with channel ID if available
    if @user.youtube_channel_id.present?
      begin
        channels = @service.list_channels('snippet,statistics', id: @user.youtube_channel_id)
        results[:by_stored_id] = {
          count: channels.items&.count || 0,
          channels: channels.items&.map { |c| { id: c.id, title: c.snippet.title } } || []
        }
      rescue Google::Apis::Error => e
        results[:by_stored_id] = { error: e.message }
      end
    end

    results
  end

  private

  def test_api_connection
    # Simple test call
    response = @service.list_channels('id', mine: true, max_results: 1)
    true
  rescue Google::Apis::Error => e
    false
  end

  def generate_recommendations(diagnostics)
    checks = diagnostics[:checks]

    channels_mine = checks[:channels_mine_count] || 0
    managed_channels = checks[:managed_channels_count] || 0

    if channels_mine == 0 && managed_channels == 0
      diagnostics[:recommendations] << 'No channels found with current token. Possible issues:'
      diagnostics[:recommendations] << '1. User may not have a YouTube channel created'
      diagnostics[:recommendations] << '2. Channel may be a Brand Account requiring different API calls'
      diagnostics[:recommendations] << '3. YouTube account may be suspended or restricted'
      diagnostics[:recommendations] << '4. OAuth scope may be insufficient'
      diagnostics[:recommendations] << '5. API key referrer restrictions may be blocking requests'
    end

    diagnostics[:recommendations] << 'Access token is expired and needs refresh' if checks[:token_expired]

    if checks[:scopes] && !checks[:scopes].include?('https://www.googleapis.com/auth/youtube.readonly')
      diagnostics[:recommendations] << 'Missing required YouTube scope. User needs to upgrade OAuth permissions.'
    end

    if managed_channels > 0 && channels_mine == 0
      diagnostics[:recommendations] << 'User has Brand Account channels. Consider using managed channels.'
    end

    # Check for API key referrer issues
    return unless checks[:api_auth_error]&.include?('referer') || checks[:api_error]&.include?('referer')

    diagnostics[:recommendations] << 'API key has referrer restrictions. Remove restrictions or add server domain.'
  end
end

# Add this method to your YouTubeAuthController for debugging:
def debug_channel_info
  return render json: { error: 'Not available in production' }, status: :forbidden if Rails.env.production?

  diagnostics_service = YoutubeDiagnosticsService.new(current_devise_api_user)
  diagnostics = diagnostics_service.diagnose_channel_issues
  test_results = diagnostics_service.test_channel_retrieval_methods

  render json: {
    diagnostics:,
    test_results:
  }, status: :ok
end
