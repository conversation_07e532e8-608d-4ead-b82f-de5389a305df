class YoutubeDataResetService
  include ActiveModel::Model

  attr_accessor :user, :initiated_by, :reason

  def initialize(user:, initiated_by: nil, reason: 'manual_reset')
    @user = user
    @initiated_by = initiated_by
    @reason = reason
  end

  def perform
    return { success: false, error: 'User not found' } unless user
    return { success: false, error: 'User has no YouTube connection' } unless user.youtube_connected?

    ActiveRecord::Base.transaction do
      # Check for dependent leagues before reset
      dependent_leagues = check_dependent_leagues

      # Store original data for logging
      original_data = capture_original_data

      # Perform the reset
      clear_oauth_tokens
      clear_cached_data
      reset_creator_status
      clear_subscription_data
      update_user_youtube_fields

      # Log the reset operation
      log_reset_operation(original_data, dependent_leagues)

      {
        success: true,
        message: 'YouTube data reset successfully',
        dependent_leagues:,
        warnings: generate_warnings(dependent_leagues)
      }
    end
  rescue StandardError => e
    Rails.logger.error "YouTube data reset failed for user #{user.id}: #{e.message}"
    { success: false, error: e.message }
  end

  private

  def check_dependent_leagues
    return [] unless user.creator_status?

    League.where(creator: user, subscriber_only: true).map do |league|
      {
        id: league.id,
        name: league.name,
        member_count: league.memberships.count
      }
    end
  end

  def capture_original_data
    {
      youtube_channel_id: user.youtube_channel_id,
      youtube_channel_name: user.youtube_channel_name,
      creator_status: user.creator_status?,
      subscriber_count: user.subscriber_count,
      tokens_present: user.youtube_tokens.exists?
    }
  end

  def clear_oauth_tokens
    user.youtube_tokens.destroy_all
    Rails.logger.info "Cleared OAuth tokens for user #{user.id}"
  end

  def clear_cached_data
    return unless user.youtube_channel_id.present?

    # Clear Rails cache
    cache_keys = [
      "youtube_channel_#{user.youtube_channel_id}",
      "youtube_subscriptions_#{user.id}",
      "user_youtube_data_#{user.id}",
      "creator_eligibility_#{user.id}"
    ]

    cache_keys.each do |key|
      Rails.cache.delete(key)
    end

    # Clear Redis cache patterns
    begin
      redis = RedisConnection.redis
      patterns = [
        "youtube_api:subscription:#{user.id}:*",
        "youtube_api:channel:#{user.youtube_channel_id}:*",
        "user_initiated_verification:#{user.id}"
      ]

      patterns.each do |pattern|
        keys = redis.keys(pattern)
        redis.del(keys) if keys.any?
      end
    rescue StandardError => e
      Rails.logger.warn "Failed to clear Redis cache: #{e.message}"
    end

    Rails.logger.info "Cleared cached YouTube data for user #{user.id}"
  end

  def reset_creator_status
    return unless user.creator_status?

    user.update!(
      creator_status: false,
      subscriber_count: nil,
      creator_verified_at: nil
    )
    Rails.logger.info "Reset creator status for user #{user.id}"
  end

  def clear_subscription_data
    # Clear any stored subscription verification data
    # This might include database records if you store subscription history

    redis = RedisConnection.redis
    verification_keys = redis.keys("subscription_verification:#{user.id}:*")
    redis.del(verification_keys) if verification_keys.any?
  rescue StandardError => e
    Rails.logger.warn "Failed to clear subscription verification data: #{e.message}"
  end

  def update_user_youtube_fields
    user.update!(
      youtube_channel_id: nil,
      youtube_channel_name: nil,
      youtube_connected_at: nil
    )
    Rails.logger.info "Cleared YouTube connection fields for user #{user.id}"
  end

  def log_reset_operation(original_data, dependent_leagues)
    log_data = {
      user_id: user.id,
      user_email: user.email,
      initiated_by: initiated_by&.id || 'system',
      initiated_by_email: initiated_by&.email,
      reason:,
      timestamp: Time.current,
      original_data:,
      dependent_leagues_count: dependent_leagues.count,
      dependent_leagues:
    }

    Rails.logger.info "YouTube data reset completed: #{log_data.to_json}"

    # You might want to store this in a dedicated audit log table
    # AuditLog.create!(
    #   action: 'youtube_data_reset',
    #   user: user,
    #   initiated_by: initiated_by,
    #   details: log_data
    # )
  end

  def generate_warnings(dependent_leagues)
    warnings = []

    if dependent_leagues.any?
      warnings << "User was creator of #{dependent_leagues.count} subscriber-only league(s). These leagues may need attention."
    end

    warnings << 'User will need to reconnect YouTube account to access subscriber-only leagues.'
    warnings << 'Cached subscription data has been cleared and will need to be re-verified.'

    warnings
  end
end
