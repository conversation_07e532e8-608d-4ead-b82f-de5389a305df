class LeagueRoundVisibility < ApplicationRecord
  belongs_to :league
  belongs_to :season
  belongs_to :made_visible_by_user, class_name: 'User', optional: true

  has_many :showcases, class_name: 'LeagueRoundVisibilityShowcase', dependent: :destroy
  has_many :showcased_users, through: :showcases, source: :user

  validates :round_number, presence: true, numericality: { only_integer: true, greater_than: 0 }

  validates :visible, inclusion: { in: [true, false] }
  validates :made_visible_by_user, presence: true, if: :visible?
  validates :visible_at, presence: true, if: :visible?

  before_update :prevent_unpublish_after_grace_period

  # Check if this round can be unpublished (within 1-day grace period)
  def can_unpublish?
    return true unless visible? && visible_at.present?

    Time.current <= visible_at + 1.day
  end

  private

  def prevent_unpublish_after_grace_period
    return unless visible_changed? && visible_change == [true, false]

    return if can_unpublish?

    errors.add(:visible, 'cannot be unpublished after the 1-day grace period has expired')
    throw :abort
  end
end
