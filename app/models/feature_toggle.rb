# app/models/feature_toggle.rb
class FeatureToggle < ApplicationRecord
  validates :name, presence: true, uniqueness: true
  validates :description, presence: true

  # Check if a user has access to this feature
  def user_has_access?(user_id)
    return false unless enabled?
    return true if allowed_user_ids.empty? # If no specific users, allow all

    allowed_user_ids.include?(user_id.to_s)
  end

  # Class method to check feature access
  def self.feature_enabled_for_user?(feature_name, user_id)
    toggle = find_by(name: feature_name)
    return false unless toggle

    toggle.user_has_access?(user_id)
  end

  # Get all features accessible to a user
  def self.features_for_user(user_id)
    enabled.select { |toggle| toggle.user_has_access?(user_id) }
  end

  scope :enabled, -> { where(enabled: true) }
end
