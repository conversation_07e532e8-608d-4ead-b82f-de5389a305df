class AppSetting < ApplicationRecord
  NUMERIC_INT_KEYS = %w[
    default_league_join_limit
    content_creator_league_join_limit
  ].freeze

  validates :key, presence: true, uniqueness: true
  validates :value, presence: true
  validate :validate_numeric_keys

  # Fetch plain string value with default fallback
  def self.fetch(key, default = nil)
    find_by(key:)&.value || default
  end

  # Fetch integer value with default fallback
  def self.fetch_int(key, default = nil)
    val = fetch(key, default)
    Integer(val)
  rescue ArgumentError, TypeError
    default
  end

  private

  def validate_numeric_keys
    return unless NUMERIC_INT_KEYS.include?(key)

    begin
      iv = Integer(value)
      errors.add(:value, 'must be >= 1') if iv < 1
    rescue ArgumentError, TypeError
      errors.add(:value, 'must be an integer')
    end
  end
end
