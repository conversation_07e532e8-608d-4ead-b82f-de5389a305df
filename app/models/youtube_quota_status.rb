# app/models/youtube_quota_status.rb
class YoutubeQuotaStatus < ApplicationRecord
  validates :quota_date, presence: true, uniqueness: true
  validates :current_usage, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :daily_limit, presence: true, numericality: { greater_than: 0 }
  validates :buffer_threshold, presence: true, numericality: { greater_than_or_equal_to: 0 }

  scope :for_date, ->(date) { where(quota_date: date) }
  scope :exhausted, -> { where(quota_exhausted: true) }
  scope :current, -> { for_date(Date.current) }

  TEMPORARILY_LIMITED = 'temporarily_limited'.freeze # 'YouTube features temporarily limited due to API quota'
  HIGH_USAGE_WARNING  = 'high_usage_warning'.freeze # 'YouTube features may be limited due to high usage'
  FULLY_AVAILABLE     = 'fully_available'.freeze # 'YouTube features fully available'

  # Class methods for easy access
  def self.current_status
    for_date(Date.current).first || create_for_today
  end

  def self.create_for_today
    create!(
      quota_date: Date.current,
      current_usage: 0,
      daily_limit: ENV.fetch('YOUTUBE_API_DAILY_QUOTA', 10_000).to_i,
      buffer_threshold: ENV.fetch('YOUTUBE_API_QUOTA_BUFFER', 1_000).to_i
    )
  end

  def self.usage_today
    current_status.current_usage
  end

  def self.remaining_quota_today
    status = current_status
    [0, status.daily_limit - status.current_usage].max
  end

  def self.is_quota_exhausted?
    current_status.quota_exhausted?
  end

  def self.is_near_quota_limit?
    status = current_status
    remaining = status.daily_limit - status.current_usage
    remaining <= status.buffer_threshold
  end

  # Instance methods
  def usage_percentage
    return 0 if daily_limit.zero?

    [(current_usage.to_f / daily_limit * 100).round(1), 100.0].min
  end

  def remaining_quota
    [0, daily_limit - current_usage].max
  end

  def near_limit?
    remaining_quota <= buffer_threshold
  end

  def can_accommodate?(units_needed)
    return false if quota_exhausted?

    remaining_quota >= (units_needed + buffer_threshold)
  end

  def mark_exhausted!(reason = nil)
    update!(
      quota_exhausted: true,
      exhausted_at: Time.current,
      last_exhaustion_reason: reason
    )
  end

  def reset_quota!
    update!(
      current_usage: 0,
      quota_exhausted: false,
      exhausted_at: nil,
      last_exhaustion_reason: nil
    )
  end
end
