# Match model

class Match < ApplicationRecord
  SOURCES = %w[football_data api_football ksi_soap].freeze

  validates :source, inclusion: { in: SOURCES }
  validates :external_service_id, presence: true, uniqueness: { scope: %i[source season_id] }

  belongs_to :season
  has_one :score
  has_many :match_predictions
  belongs_to :home_team, class_name: 'Team', optional: true
  belongs_to :away_team, class_name: 'Team', optional: true
  validates :season, presence: true
  # Only validate team presence for certain statuses
  validates :home_team_id, :away_team_id, presence: true,
                                          if: -> { %w[LIVE IN_PLAY PAUSED FINISHED].include?(status) }
  validates :status, inclusion: { in: %w[
    SCHEDULED
    TIMED
    LIVE
    IN_PLAY
    PAUSED
    FINISHED
    POSTPONED
    SUSPENDED
    CANCELLED
    AWARDED
  ], message: '%<value>s is not a valid status' }

  enum stage: {
    FINAL: 'FINAL',
    THIRD_PLACE: 'THIRD_PLACE',
    SEMI_FINALS: 'SEMI_FINALS',
    QUARTER_FINALS: 'QUARTER_FINALS',
    LEAGUE_STAGE: 'LEAGUE_STAGE',
    LAST_16: 'LAST_16',
    LAST_32: 'LAST_32',
    LAST_64: 'LAST_64',
    ROUND_4: 'ROUND_4',
    ROUND_3: 'ROUND_3',
    ROUND_2: 'ROUND_2',
    ROUND_1: 'ROUND_1',
    GROUP_STAGE: 'GROUP_STAGE',
    PRELIMINARY_ROUND: 'PRELIMINARY_ROUND',
    QUALIFICATION: 'QUALIFICATION',
    QUALIFICATION_ROUND_1: 'QUALIFICATION_ROUND_1',
    QUALIFICATION_ROUND_2: 'QUALIFICATION_ROUND_2',
    QUALIFICATION_ROUND_3: 'QUALIFICATION_ROUND_3',
    PLAYOFF_ROUND_1: 'PLAYOFF_ROUND_1',
    PLAYOFF_ROUND_2: 'PLAYOFF_ROUND_2',
    PLAYOFFS: 'PLAYOFFS',
    REGULAR_SEASON: 'REGULAR_SEASON',
    CLAUSURA: 'CLAUSURA',
    APERTURA: 'APERTURA',
    CHAMPIONSHIP: 'CHAMPIONSHIP',
    RELEGATION: 'RELEGATION',
    RELEGATION_ROUND: 'RELEGATION_ROUND'
  }

  after_update :check_season_matchday_update, if: :saved_change_to_status?
  after_update :log_status_change, if: :saved_change_to_status?
  after_save :calculate_predictions, if: :finished_and_scored?

  def finished?
    %w[FINISHED POSTPONED].include?(status)
  end

  private

  def check_season_matchday_update
    season.update_matchday_if_complete if %w[FINISHED POSTPONED].include?(status)
  end

  def finished_and_scored?
    saved_change_to_status? && status == 'FINISHED'
  end

  def calculate_predictions
    log_match_change(self, 'calculating_predictions', { predictions_count: match_predictions.count })
    match_predictions.each(&:calculate_points)
  end

  def log_status_change
    old_status, new_status = saved_change_to_status
    log_match_change(self, 'status_changed', {
                       old_status:,
                       new_status:,
                       utc_date:
                     })
  end
end
