class LeagueCustomization < ApplicationRecord
  belongs_to :league

  HEADER_PLACEMENTS = %w[navbar above_tabs below_tabs].freeze
  HEADER_ALIGNMENTS = %w[left center right].freeze
  LOGO_POSITIONS    = %w[left right].freeze
  LOGO_SIZES        = %w[small medium large].freeze

  # Enhanced color validation
  HEX_REGEX = /\A#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})\z/
  NAMED_COLORS = %w[transparent inherit initial unset none].freeze
  RGB_REGEX = /\Argba?\(\s*(?:\d{1,3}(?:\.\d+)?%?)\s*,\s*(?:\d{1,3}(?:\.\d+)?%?)\s*,\s*(?:\d{1,3}(?:\.\d+)?%?)\s*(?:,\s*(?:0?\.\d+|1(?:\.0+)?|\d{1,3}%))?\s*\)\z/i
  HSL_REGEX = /\Ahsla?\(\s*(?:\d{1,3}(?:\.\d+)?)\s*,\s*(?:\d{1,3}(?:\.\d+)?%)\s*,\s*(?:\d{1,3}(?:\.\d+)?%)\s*(?:,\s*(?:0?\.\d+|1(?:\.0+)?|\d{1,3}%))?\s*\)\z/i

  validates :league_id, presence: true, uniqueness: true
  validates :custom_header, length: { maximum: 120 }, allow_nil: true
  validates :header_font, length: { maximum: 64 }, allow_nil: true
  validates :header_font_size, numericality: { greater_than_or_equal_to: 10, less_than_or_equal_to: 72 },
                               allow_nil: true
  validates :header_placement, inclusion: { in: HEADER_PLACEMENTS }, allow_nil: true
  validates :header_alignment, inclusion: { in: HEADER_ALIGNMENTS }, allow_nil: true
  validates :logo_position, inclusion: { in: LOGO_POSITIONS }, allow_nil: true
  validates :logo_size, inclusion: { in: LOGO_SIZES }, allow_nil: true

  validate :validate_color_fields
  validate :logo_url_format

  private

  def validate_color_fields
    color_fields = %w[
      primary primary_foreground secondary secondary_foreground accent accent_foreground
      highlight highlight_foreground muted muted_foreground destructive destructive_foreground
      navbar_border
    ]

    color_fields.each do |field|
      value = send(field)
      next if value.blank?

      unless valid_color_format?(value)
        errors.add(field, 'must be a valid color (hex, rgb, hsl, or named color like transparent)')
      end
    end
  end

  def logo_url_format
    return if logo_url.blank?

    uri = begin
      URI.parse(logo_url)
    rescue StandardError
      nil
    end

    return if uri&.is_a?(URI::HTTP) || uri&.is_a?(URI::HTTPS)

    errors.add(:logo_url, 'must be a valid URL')
  end

  def valid_color_format?(value)
    return true if value.blank?
    return true if NAMED_COLORS.include?(value.downcase)
    return true if value.match?(HEX_REGEX)
    return true if value.match?(RGB_REGEX)
    return true if value.match?(HSL_REGEX)

    false
  end
end
