# app/lib/youtube_cache.rb
# Cache helper that guarantees a working cache even when Rails.cache is a NullStore (e.g., in test)
module YoutubeCache
  class << self
    def store
      # Use Rails.cache unless it is a NullStore; otherwise fall back to a process-local MemoryStore
      if rails_cache_is_usable?
        Rails.cache
      else
        memory_store
      end
    end

    def read(key)
      store.read(key)
    end

    def write(key, value, **options)
      store.write(key, value, **options)
    end

    def fetch(key, **options, &block)
      store.fetch(key, **options, &block)
    end

    private

    def rails_cache_is_usable?
      defined?(Rails) && Rails.respond_to?(:cache) && !Rails.cache.is_a?(ActiveSupport::Cache::NullStore)
    end

    def memory_store
      @memory_store ||= ActiveSupport::Cache::MemoryStore.new
    end
  end
end

