# app/controllers/concerns/feature_gated.rb
module FeatureGated
  extend ActiveSupport::Concern

  private

  # Check if user has access to a feature, raise error if not
  def require_feature!(feature_name)
    unless feature_enabled_for_user?(feature_name)
      render json: {
        error: 'Feature not available',
        message: "The #{feature_name.humanize.downcase} feature is not available for your account."
      }, status: :forbidden
      return false
    end
    true
  end

  # Check if user has access to a feature, returns boolean
  def feature_enabled_for_user?(feature_name)
    FeatureToggle.feature_enabled_for_user?(feature_name, current_devise_api_user.id)
  end

  # Before action helper
  def check_feature_access(feature_name)
    proc { require_feature!(feature_name) }
  end

  class_methods do
    # Class method to set up before_action for feature gating
    def feature_gate(feature_name, options = {})
      before_action -> { require_feature!(feature_name) }, options
    end
  end
end
