module AdminCacheInvalidation
  extend ActiveSupport::Concern

  included do
    after_action :invalidate_admin_caches, only: %i[create update destroy]
  end

  private

  def invalidate_admin_caches
    return unless should_invalidate_cache?

    resource_type = controller_name.singularize
    resource = instance_variable_get("@#{resource_type}")

    return unless resource

    additional_context = build_additional_context(resource)

    AdminCacheInvalidationService.invalidate_for(
      resource_type,
      resource,
      additional_context
    )
  end

  def should_invalidate_cache?
    # Only invalidate for successful operations
    response.successful? && !response.redirect?
  end

  def build_additional_context(resource)
    context = {}

    # Add common context based on resource type
    case controller_name
    when 'teams'
      context[:competition_id] = params[:competition_id] if params[:competition_id]
      context[:season_id] = params[:season_id] if params[:season_id]
    when 'matches'
      if resource.respond_to?(:season)
        context[:season_id] = resource.season_id
        context[:competition_code] = resource.season.competition.code
      end
    end

    context
  end
end
