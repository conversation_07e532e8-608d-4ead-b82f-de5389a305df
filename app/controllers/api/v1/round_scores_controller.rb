module Api
  module V1
    class RoundScoresController < ApplicationController
      before_action :authenticate_devise_api_token!

      # GET /api/v1/leagues/:id/rounds/:round_number/scores
      def league_round_scores
        league = League.find(params[:id])
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || league.season_id).to_i

        cache_key = "league_#{league.id}_season_#{season_id}_round_#{round_number}_scores"
        scores = Rails.cache.fetch(cache_key, expires_in: 10.minutes) do
          full_visible = can_view_round_scores?(league, season_id, round_number, current_devise_api_user)
          auto_limit = full_visible ? 0 : auto_showcase_limit(league, season_id, round_number)
          if !full_visible && auto_limit <= 0
            nil
          else
            all_scores = league.calculate_standings_for_round(round_number, season_id)
            auto_limit.positive? ? all_scores.first(auto_limit) : all_scores
          end
        end

        # TODO: Add match details to scores
        #  match_details?: Array<{
        #   match_id: number;
        #   home_team: string;
        #   away_team: string;
        #   home_team_crest?: string;
        #   away_team_crest?: string;
        #   actual_score: { home: number | null; away: number | null };
        #   predicted_score: { home: number; away: number };
        #   points_earned: 0 | 1 | 3;
        # }>;

        if scores.nil?
          render json: { error_key: 'LEAGUE_ROUND_SCORES_HIDDEN' }, status: :forbidden
        else
          render json: { league_id: league.id, round_number:, scores: }
        end
      end

      # GET /api/v1/leagues/:id/rounds/:round_number/winners
      def league_round_winners
        league = League.find(params[:id])
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || league.season_id).to_i

        full_visible = can_view_round_scores?(league, season_id, round_number, current_devise_api_user)
        auto_limit = full_visible ? 0 : auto_showcase_limit(league, season_id, round_number)
        if !full_visible && auto_limit <= 0
          render json: { error_key: 'LEAGUE_ROUND_SCORES_HIDDEN' }, status: :forbidden and return
        end

        user_ids = league.users.pluck(:id)
        rps = RoundPrediction
              .where(user_id: user_ids, season_id:, matchday: round_number)
              .sort_by { |rp| -rp.total_points }

        limit = full_visible ? 3 : [auto_limit, 3].min
        top = rps.first(limit)
        winners = top.map.with_index(1) do |rp, idx|
          { rank: idx, user_id: rp.user_id, username: rp.user.username, total_points: rp.total_points }
        end

        render json: { league_id: league.id, round_number:, winners: }
      end

      # GET /api/v1/users/:id/rounds/:round_number/score
      # Optional: ?league_id= to enforce creator-controlled showcase visibility
      def user_round_score # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
        user = User.find(params[:id])
        round_number = params[:round_number].to_i
        season_id = params[:season_id]

        rp = RoundPrediction.includes(:match_predictions).find_by(user_id: user.id, season_id:, matchday: round_number)
        render json: { error: 'No round prediction found' }, status: :not_found and return unless rp

        if params[:league_id].present?
          league = League.find_by(id: params[:league_id])
          render json: { error_key: 'LEAGUE_NOT_FOUND' }, status: :not_found and return unless league

          # Permission: viewer can always see if owner/admin or league not creator-controlled
          if league.scoring_visibility_mode == 'creator_controlled' &&
             !(current_devise_api_user&.admin? || current_devise_api_user&.id == league.owner_id)

            # If round is published, it's visible
            published = LeagueRoundVisibility.find_by(league_id: league.id, season_id: season_id || league.season_id,
                                                      round_number:, visible: true)
            unless published
              # If not published: allow if showcased for this user
              lrv = LeagueRoundVisibility.find_by(league_id: league.id, season_id: season_id || league.season_id,
                                                  round_number:)
              showcased = lrv&.showcased_users&.exists?(id: user.id)

              unless showcased # rubocop:disable Metrics/BlockNesting
                # Allow if within automatic top-N showcase
                limit = auto_showcase_limit(league, (season_id || league.season_id), round_number)
                if limit.positive? # rubocop:disable Metrics/BlockNesting
                  # check if user is in top N
                  user_ids = league.users.pluck(:id)
                  rps = RoundPrediction.where(user_id: user_ids, season_id: season_id || league.season_id,
                                              matchday: round_number)
                  top_ids = rps.sort_by { |p| -p.total_points }.first(limit).map(&:user_id)
                  unless top_ids.include?(user.id) # rubocop:disable Metrics/BlockNesting
                    # Fallback to default visibility setting if no explicit record exists
                    setting = LeagueRoundVisibilitySetting.find_by(league_id: league.id)
                    default_visible = setting.nil? ? true : setting.default_visible_for_new_rounds # rubocop:disable Metrics/BlockNesting
                    unless default_visible # rubocop:disable Metrics/BlockNesting
                      render json: { error_key: 'USER_ROUND_SCORE_HIDDEN' }, status: :forbidden and return
                    end
                  end
                else
                  # Fallback to default visibility setting if no explicit record exists
                  setting = LeagueRoundVisibilitySetting.find_by(league_id: league.id)
                  default_visible = setting.nil? ? true : setting.default_visible_for_new_rounds # rubocop:disable Metrics/BlockNesting
                  unless default_visible # rubocop:disable Metrics/BlockNesting
                    render json: { error_key: 'USER_ROUND_SCORE_HIDDEN' }, status: :forbidden and return
                  end
                end
              end
            end
          end
        end

        render json: serialize_round_prediction(user, round_number, rp)
      end

      # GET /api/v1/leagues/:id/rounds/:round_number/users/:user_id/score
      def user_round_score_in_league # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
        league = League.find(params[:id])
        user = User.find(params[:user_id])
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || league.season_id).to_i

        rp = RoundPrediction.includes(:match_predictions).find_by(user_id: user.id, season_id:, matchday: round_number)
        render json: { error: 'No round prediction found' }, status: :not_found and return unless rp

        allowed = can_view_round_scores?(league, season_id, round_number, current_devise_api_user)
        showcased = showcased_user?(league, season_id, round_number, user.id)
        if !allowed && !showcased
          limit = auto_showcase_limit(league, season_id, round_number)
          if limit.positive? # rubocop:disable Style/GuardClause
            user_ids = league.users.pluck(:id)
            rps = RoundPrediction.where(user_id: user_ids, season_id:, matchday: round_number)
            top_ids = rps.sort_by { |p| -p.total_points }.first(limit).map(&:user_id)
            unless top_ids.include?(user.id)
              render json: { error_key: 'USER_ROUND_SCORE_HIDDEN' }, status: :forbidden and return
            end
          else
            render json: { error_key: 'USER_ROUND_SCORE_HIDDEN' }, status: :forbidden and return
          end
        end
        render json: serialize_round_prediction(user, round_number, rp).merge(league_id: league.id)
      end

      private

      def serialize_round_prediction(user, round_number, rps)
        {
          user_id: user.id,
          username: user.username,
          round_number:,
          total_points: rps.total_points,
          breakdown: rps.match_predictions.map { |mp| { match_id: mp.match_id, points: mp.points } }
        }
      end

      def showcased_user?(league, season_id, round_number, user_id)
        lrv = LeagueRoundVisibility.find_by(league_id: league.id, season_id:, round_number:)
        return false unless lrv

        lrv.showcased_users.exists?(id: user_id)
      end

      def can_view_round_scores?(league, season_id, round_number, user)
        return true if user.admin? || user.id == league.owner_id

        # Explicit publish wins
        if season_id.present?
          published = LeagueRoundVisibility.find_by(league_id: league.id, season_id:,
                                                    round_number:, visible: true)
          return true if published
        end
        # Otherwise follow default setting (default true if no record)
        setting = LeagueRoundVisibilitySetting.find_by(league_id: league.id)
        setting.nil? ? true : setting.default_visible_for_new_rounds
      end

      def auto_showcase_limit(league, season_id, round_number)
        return 0 unless league.scoring_visibility_mode == 'creator_controlled'

        # Must not be published
        published = LeagueRoundVisibility.find_by(league_id: league.id, season_id:, round_number:, visible: true)
        return 0 if published

        # If default visibility is true, full scores are already visible
        setting = LeagueRoundVisibilitySetting.find_by(league_id: league.id)
        default_visible = setting.nil? ? true : setting.default_visible_for_new_rounds
        return 0 if default_visible

        limit = league.showcase_user_limit.to_i
        limit.positive? ? limit : 0
      end
    end
  end
end
