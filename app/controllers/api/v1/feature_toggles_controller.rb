# app/controllers/api/v1/feature_toggles_controller.rb
module Api
  module V1
    class FeatureTogglesController < ApplicationController
      before_action :authenticate_devise_api_token!
      # before_action :authenticate_admin!

      # GET /api/v1/feature_toggles
      # Returns all feature toggles accessible to the current user
      def index
        features = FeatureToggle.features_for_user(current_devise_api_user.id)

        render json: {
          features: features.map do |toggle|
            {
              id: toggle.id,
              name: toggle.name,
              description: toggle.description,
              enabled: toggle.enabled
            }
          end
        }
      end

      # GET /api/v1/feature_toggles/:name
      # Check if a specific feature is enabled for the current user
      def show
        feature_name = params[:name]
        enabled = FeatureToggle.feature_enabled_for_user?(feature_name, current_devise_api_user.id)

        render json: {
          name: feature_name,
          enabled:
        }
      end

      private

      def toggle_params
        params.require(:feature_toggle).permit(:name, :description, :enabled, allowed_user_ids: [])
      end
    end
  end
end
