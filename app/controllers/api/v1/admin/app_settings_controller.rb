module Api
  module V1
    module Admin
      class AppSettingsController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        before_action :set_app_setting, only: %i[show update]

        # GET /api/v1/admin/app_settings
        def index
          settings = ::AppSetting.order(:key)
          render json: { data: settings.as_json(only: %i[key value]) }
        end

        # GET /api/v1/admin/app_settings/:key
        def show
          if @setting
            render json: { data: @setting.as_json(only: %i[key value]) }
          else
            render json: { error: 'Setting not found' }, status: :not_found
          end
        end

        # POST /api/v1/admin/app_settings
        # Body: { key: 'default_league_join_limit', value: '12' }
        def create
          setting = ::AppSetting.find_or_initialize_by(key: setting_params[:key])
          setting.value = setting_params[:value]

          if setting.save
            render json: { data: setting.as_json(only: %i[key value]) }, status: :created
          else
            render json: { errors: setting.errors.full_messages }, status: :unprocessable_entity
          end
        end

        # PUT/PATCH /api/v1/admin/app_settings/:key
        def update
          @setting = ::AppSetting.new(key: params[:key]) if @setting.nil?

          @setting.value = setting_params[:value]

          if @setting.save
            render json: { data: @setting.as_json(only: %i[key value]) }
          else
            render json: { errors: @setting.errors.full_messages }, status: :unprocessable_entity
          end
        end

        private

        def set_app_setting
          k = params[:key] || params[:id]
          @setting = ::AppSetting.find_by(key: k)
        end

        def setting_params
          params.require(:app_setting).permit(:key, :value)
        end
      end
    end
  end
end
