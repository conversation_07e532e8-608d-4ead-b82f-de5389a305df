module Api
  module V1
    module Admin
      class UsersController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        before_action :set_user, only: %i[show update destroy restore toggle_admin]

        # GET /api/v1/admin/users
        def index
          users = User.all
          users = users.where('email ILIKE ?', "%#{params[:email]}%") if params[:email].present?
          users = users.where('username ILIKE ?', "%#{params[:username]}%") if params[:username].present?
          users = users.where('created_at >= ?', params[:registered_after]) if params[:registered_after].present?
          users = users.where('created_at <= ?', params[:registered_before]) if params[:registered_before].present?

          case params[:status]
          when 'active'
            users = users.where(deleted_at: nil)
          when 'deleted'
            users = users.where.not(deleted_at: nil)
          end

          # TODO: league count filter (requires join)

          users = users.order(created_at: :desc)
          users = users.page(params[:page]).per(params.fetch(:per_page, 25)) if users.respond_to?(:page)

          render json: {
            data: users.as_json(only: %i[id email username admin created_at],
                                methods: %i[youtube_channel_id youtube_channel_name
                                            is_content_creator youtube_subscriber_count]),
            pagination: pagination_meta(users)
          }
        end

        # GET /api/v1/admin/users/:id
        def show
          render json: {
            data: @user.as_json(only: %i[id email username admin created_at updated_at],
                                methods: %i[youtube_channel_id youtube_channel_name
                                            is_content_creator youtube_subscriber_count]),
            memberships: @user.memberships.includes(:league).map do |m|
                           { league_id: m.league_id, league_name: m.league.name, joined_at: m.created_at }
                         end,
            predictions_count: @user.match_predictions.count
          }
        end

        # PATCH /api/v1/admin/users/:id
        def update
          if @user.update(user_params)
            render json: { data: @user.slice(:id, :email, :username, :admin) }
          else
            render json: { errors: @user.errors.full_messages }, status: :unprocessable_entity
          end
        end

        # DELETE /api/v1/admin/users/:id (soft delete)
        def destroy
          if @user.update(deleted_at: Time.current)
            render json: { message: 'User soft-deleted' }
          else
            render json: { errors: @user.errors.full_messages }, status: :unprocessable_entity
          end
        end

        # POST /api/v1/admin/users/:id/restore
        def restore
          if @user.update(deleted_at: nil)
            render json: { message: 'User restored' }
          else
            render json: { errors: @user.errors.full_messages }, status: :unprocessable_entity
          end
        end

        # POST /api/v1/admin/users/:id/toggle_admin
        def toggle_admin
          @user.update(admin: !@user.admin)
          Rails.logger.info("[AUDIT] Admin #{current_devise_api_user&.id} toggled admin for user #{@user.id} to #{@user.admin}")
          render json: { data: Api::V1::Admin::UserSerializer.new(@user).as_json }
        rescue StandardError => e
          render json: { errors: [e.message] }, status: :unprocessable_entity
        end

        private

        def set_user
          @user = User.find(params[:id])
        end

        def user_params
          params.require(:user).permit(:email, :username, :admin)
        end

        def pagination_meta(scope)
          return {} unless scope.respond_to?(:current_page)

          {
            page: scope.current_page,
            per_page: scope.limit_value,
            total_pages: scope.total_pages,
            total_count: scope.total_count
          }
        end
      end
    end
  end
end
