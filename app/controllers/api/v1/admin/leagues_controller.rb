# app/controllers/api/v1/admin/leagues_controller.rb
module Api
  module V1
    module Admin
      class LeaguesController < ApplicationController
        before_action :authenticate_devise_api_token!
        before_action :authenticate_admin!
        before_action :set_league, only: %i[show update destroy transfer_ownership]

        # GET /api/v1/admin/leagues
        def index
          leagues = League.includes(:owner, :season, :memberships)
          leagues = apply_filters(leagues)
          leagues = leagues.order(created_at: :desc)
          leagues = leagues.page(params[:page]).per(params.fetch(:per_page, 25)) if leagues.respond_to?(:page)

          render json: {
            data: ActiveModelSerializers::SerializableResource.new(
              leagues,
              each_serializer: Api::V1::Admin::LeagueSerializer
            ),
            pagination: pagination_meta(leagues),
            filters: index_filters_from_params
          }
        end

        # GET /api/v1/admin/leagues/:id
        def show
          render json: {
            data: Api::V1::Admin::LeagueSerializer.new(@league).as_json
          }
        end

        # PUT/PATCH /api/v1/admin/leagues/:id
        def update
          if @league.update(league_params)
            Rails.logger.info(
              "[AUDIT] Admin #{current_devise_api_user&.id} updated league #{@league.id}"
            )
            render json: { data: Api::V1::Admin::LeagueSerializer.new(@league).as_json }
          else
            render json: { errors: @league.errors.full_messages }, status: :unprocessable_entity
          end
        end

        # DELETE /api/v1/admin/leagues/:id (soft delete -> archive)
        def destroy
          @league.archive!
          Rails.logger.info(
            "[AUDIT] Admin #{current_devise_api_user&.id} archived league #{@league.id}"
          )
          render json: { message: 'League archived' }
        rescue StandardError => e
          render json: { errors: [e.message] }, status: :unprocessable_entity
        end

        # POST /api/v1/admin/leagues/:id/transfer_ownership
        def transfer_ownership
          new_owner_id = transfer_params[:new_owner_id]
          new_owner = User.find_by(id: new_owner_id)
          return render json: { errors: ['New owner not found'] }, status: :not_found unless new_owner

          service = ::Admin::LeagueOwnershipTransferService.new(
            league: @league,
            new_owner:,
            performed_by: current_devise_api_user
          )

          if service.call
            render json: { message: 'Ownership transferred', data: Api::V1::Admin::LeagueSerializer.new(@league.reload).as_json }
          else
            render json: { errors: service.errors }, status: :unprocessable_entity
          end
        end

        # POST /api/v1/admin/leagues/bulk_action
        def bulk_action
          ids = Array(params[:ids]).map(&:to_i).uniq
          action_name = params[:action_name]
          return render json: { errors: ['ids are required'] }, status: :bad_request if ids.empty?
          return render json: { errors: ['action_name is required'] }, status: :bad_request if action_name.blank?

          service = ::Admin::BulkLeagueOperationsService.new(
            ids:,
            action_name:,
            params: params.permit!,
            performed_by: current_devise_api_user
          )

          result = service.call
          if result[:errors].present?
            render json: result, status: :unprocessable_entity
          else
            render json: result
          end
        end

        private

        def set_league
          @league = League.find(params[:id])
        end

        def league_params
          params.require(:league).permit(
            :name,
            :open,
            :competition_id,
            :season_id,
            :starting_matchday,
            :youtube_league,
            :subscriber_only,
            :youtube_channel_id,
            :subscriber_requirement_type,
            :min_subscriber_date
          )
        end

        def transfer_params
          params.require(:transfer).permit(:new_owner_id)
        end

        def apply_filters(scope)
          s = scope
          s = s.where(archived: ActiveModel::Type::Boolean.new.cast(params[:archived])) if params.key?(:archived)
          s = s.where(open: ActiveModel::Type::Boolean.new.cast(params[:open])) if params.key?(:open)
          s = s.where(competition_id: params[:competition_id]) if params[:competition_id].present?
          s = s.where(season_id: params[:season_id]) if params[:season_id].present?
          s = s.where(owner_id: params[:owner_id]) if params[:owner_id].present?
          s = s.where(subscriber_only: ActiveModel::Type::Boolean.new.cast(params[:subscriber_only])) if params.key?(:subscriber_only)
          s = s.where(youtube_league: ActiveModel::Type::Boolean.new.cast(params[:youtube_league])) if params.key?(:youtube_league)
          if params[:q].present?
            s = s.where('leagues.name ILIKE ?', "%#{params[:q]}%")
          end
          if params[:owner_username].present?
            s = s.joins(:owner).where('users.username ILIKE ?', "%#{params[:owner_username]}%")
          end
          if params[:owner_email].present?
            s = s.joins(:owner).where('users.email ILIKE ?', "%#{params[:owner_email]}%")
          end
          s
        end

        def index_filters_from_params
          {
            archived: params[:archived],
            open: params[:open],
            competition_id: params[:competition_id],
            season_id: params[:season_id],
            owner_id: params[:owner_id],
            owner_username: params[:owner_username],
            owner_email: params[:owner_email],
            subscriber_only: params[:subscriber_only],
            youtube_league: params[:youtube_league],
            q: params[:q]
          }.compact
        end

        def pagination_meta(scope)
          return {} unless scope.respond_to?(:current_page)

          {
            page: scope.current_page,
            per_page: scope.limit_value,
            total_pages: scope.total_pages,
            total_count: scope.total_count
          }
        end
      end
    end
  end
end

