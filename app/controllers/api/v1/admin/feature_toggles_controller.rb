module Api
  module V1
    module Admin
      class FeatureTogglesController < ApplicationController
        before_action :authenticate_admin!
        before_action :authenticate_devise_api_token!
        before_action :set_feature_toggle, only: %i[show update destroy]

        def index
          toggles = FeatureToggle.all
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(
              toggles, each_serializer: FeatureToggleSerializer
            ),
            message: ['Feature toggles found'],
            status: 200,
            type: 'success'
          }
        end

        def show
          render json: {
            data: ActiveModelSerializers::SerializableResource.new(
              @feature_toggle, serializer: FeatureToggleSerializer
            ),
            message: ['Feature toggle found'],
            status: 200,
            type: 'success'
          }
        end

        def create
          toggle = FeatureToggle.new(feature_toggle_params)

          if toggle.save
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(
                toggle, serializer: FeatureToggleSerializer
              ),
              message: ['Feature toggle created successfully'],
              status: 201,
              type: 'success'
            }, status: :created
          else
            render json: {
              errors: toggle.errors.full_messages,
              status: 422
            }, status: :unprocessable_entity
          end
        end

        def update
          if @feature_toggle.update(feature_toggle_params)
            render json: {
              data: ActiveModelSerializers::SerializableResource.new(
                @feature_toggle, serializer: FeatureToggleSerializer
              ),
              message: ['Feature toggle updated successfully'],
              status: 200,
              type: 'success'
            }
          else
            render json: {
              errors: @feature_toggle.errors.full_messages,
              status: 422
            }, status: :unprocessable_entity
          end
        end

        def destroy
          @feature_toggle.destroy
          render json: {
            message: ['Feature toggle deleted successfully'],
            status: 200,
            type: 'success'
          }
        end

        private

        def set_feature_toggle
          @feature_toggle = FeatureToggle.find(params[:id])
        end

        def feature_toggle_params
          params.require(:feature_toggle).permit(:name, :description, :enabled, allowed_user_ids: [])
        end
      end
    end
  end
end
