# frozen_string_literal: true

module Api
  module V1
    class LeagueShowcasesController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :set_league
      before_action :authorize_owner!
      before_action :require_content_creator!

      # GET /api/v1/leagues/:id/showcased_users
      def index
        user_ids = LeagueUserShowcase.where(league_id: @league.id).pluck(:user_id)
        render json: { data: { league_id: @league.id, showcased_user_ids: user_ids } }, status: :ok
      end

      # POST /api/v1/leagues/:id/showcased_users
      # Body: { user_ids: number[] }
      def add
        user_ids = Array(params[:user_ids]).map(&:to_i).uniq
        return render json: { error_key: 'INVALID_PARAMS' }, status: :bad_request if user_ids.empty?

        created = []
        user_ids.each do |uid|
          rec = LeagueUserShowcase.find_or_create_by(league_id: @league.id, user_id: uid)
          created << rec.user_id if rec.persisted?
        end

        render json: { data: { league_id: @league.id,
                               showcased_user_ids: LeagueUserShowcase.where(league_id: @league.id).pluck(:user_id) } },
               status: :ok
      end

      # DELETE /api/v1/leagues/:id/showcased_users/:user_id
      def remove
        uid = params[:user_id].to_i
        LeagueUserShowcase.where(league_id: @league.id, user_id: uid).delete_all
        render json: { data: { league_id: @league.id,
                               showcased_user_ids: LeagueUserShowcase.where(league_id: @league.id).pluck(:user_id) } },
               status: :ok
      end

      private

      def set_league
        @league = League.find(params[:id])
      end

      def authorize_owner!
        return if current_devise_api_user.id == @league.owner_id

        render json: { error_key: 'LEAGUE_OWNER_ONLY' }, status: :unauthorized
      end

      def require_content_creator!
        return if current_devise_api_user.is_content_creator?

        render json: { error_key: 'NOT_CONTENT_CREATOR' }, status: :forbidden
      end
    end
  end
end
