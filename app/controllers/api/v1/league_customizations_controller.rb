module Api
  module V1
    class LeagueCustomizationsController < ApplicationController
      include FeatureGated

      before_action :authenticate_devise_api_token!
      before_action :set_league

      # GET /api/v1/leagues/:league_id/customization
      def show
        customization = @league.league_customization || @league.build_league_customization
        render json: serialize(customization)
      end

      # PUT /api/v1/leagues/:league_id/customization
      def update
        unless @league.can_be_customized_by?(current_devise_api_user)
          return render json: { error: 'unauthorized' },
                        status: :forbidden
        end

        # Check which features the user is trying to update and validate access
        requested_params = params.require(:customization)

        # Check if user is trying to update header/logo features
        if updating_header_logo_features?(requested_params) &&
           !feature_enabled_for_user?('League-customization')
          return render json: {
            error: 'Feature not available',
            message: 'League header and logo customization is not available for your account.'
          }, status: :forbidden
        end

        # Check if user is trying to update color scheme features
        if updating_color_scheme_features?(requested_params) &&
           !feature_enabled_for_user?('League-customization-colorscheme')
          return render json: {
            error: 'Feature not available',
            message: 'League color scheme customization is not available for your account.'
          }, status: :forbidden
        end

        customization = @league.league_customization || @league.build_league_customization
        filtered_params = filter_params_by_feature_access(customization_params)

        if customization.update(filtered_params)
          render json: serialize(customization)
        else
          render json: { errors: customization.errors.full_messages }, status: :unprocessable_entity
        end
      end

      private

      def set_league
        @league = League.find(params[:league_id])
      end

      def updating_header_logo_features?(requested_params)
        header_logo_fields = %w[customHeader headerFont headerAlignment headerFontSize headerPlacement logoUrl
                                logoPosition logoSize]
        header_logo_fields.any? { |field| requested_params.key?(field) }
      end

      def updating_color_scheme_features?(requested_params)
        requested_params.key?(:colorScheme)
      end

      def filter_params_by_feature_access(mapped_params)
        filtered = {}

        # Header/Logo features
        if feature_enabled_for_user?('League-customization')
          header_logo_fields = %w[custom_header header_font header_alignment header_font_size header_placement logo_url
                                  logo_position logo_size]
          header_logo_fields.each do |field|
            filtered[field] = mapped_params[field.to_sym] if mapped_params.key?(field.to_sym)
          end
        end

        # Color scheme features
        if feature_enabled_for_user?('League-customization-colorscheme')
          color_fields = %w[
            primary primary_foreground secondary secondary_foreground accent accent_foreground
            highlight highlight_foreground muted muted_foreground destructive destructive_foreground
          ]
          color_fields.each do |field|
            filtered[field] = mapped_params[field.to_sym] if mapped_params.key?(field.to_sym)
          end
        end

        filtered
      end

      # Accepts camelCase from FE; maps to snake_case
      def customization_params # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity,Metrics/MethodLength
        raw = params.require(:customization).permit(
          :customHeader,
          :headerFont,
          :headerAlignment,
          :headerFontSize,
          :headerPlacement,
          :logoUrl,
          :logoPosition,
          :logoSize,
          colorScheme: %i[
            primary
            primaryForeground
            secondary
            secondaryForeground
            accent
            accentForeground
            highlight
            highlightForeground
            muted
            mutedForeground
            destructive
            destructiveForeground
            navbarBorder
          ]
        )

        mapped = {}

        # Header/Logo mappings
        mapped[:custom_header] = raw[:customHeader] if raw.key?(:customHeader)
        mapped[:header_font] = raw[:headerFont] if raw.key?(:headerFont)
        mapped[:header_alignment] = raw[:headerAlignment] if raw.key?(:headerAlignment)
        mapped[:header_font_size] = raw[:headerFontSize] if raw.key?(:headerFontSize)
        mapped[:header_placement] = raw[:headerPlacement] if raw.key?(:headerPlacement)
        mapped[:logo_url] = raw[:logoUrl] if raw.key?(:logoUrl)
        mapped[:logo_position] = raw[:logoPosition] if raw.key?(:logoPosition)
        mapped[:logo_size] = raw[:logoSize] if raw.key?(:logoSize)

        # Color scheme mappings
        if raw[:colorScheme]
          cs = raw[:colorScheme]
          mapped[:primary] = cs[:primary] if cs.key?(:primary)
          mapped[:primary_foreground] = cs[:primaryForeground] if cs.key?(:primaryForeground)
          mapped[:secondary] = cs[:secondary] if cs.key?(:secondary)
          mapped[:secondary_foreground] = cs[:secondaryForeground] if cs.key?(:secondaryForeground)
          mapped[:accent] = cs[:accent] if cs.key?(:accent)
          mapped[:accent_foreground] = cs[:accentForeground] if cs.key?(:accentForeground)
          mapped[:highlight] = cs[:highlight] if cs.key?(:highlight)
          mapped[:highlight_foreground] = cs[:highlightForeground] if cs.key?(:highlightForeground)
          mapped[:muted] = cs[:muted] if cs.key?(:muted)
          mapped[:muted_foreground] = cs[:mutedForeground] if cs.key?(:mutedForeground)
          mapped[:destructive] = cs[:destructive] if cs.key?(:destructive)
          mapped[:destructive_foreground] = cs[:destructiveForeground] if cs.key?(:destructiveForeground)
          mapped[:navbar_border] = cs[:navbarBorder] if cs.key?(:navbarBorder)
        end

        mapped
      end

      def serialize(conf)
        can_customize_header_logo = @league.can_be_customized_by?(current_devise_api_user) &&
                                    feature_enabled_for_user?('League-customization')
        can_customize_colors = @league.can_be_customized_by?(current_devise_api_user) &&
                               feature_enabled_for_user?('League-customization-colorscheme')

        response = {
          leagueId: conf.league_id,
          canCustomizeLeague: @league.can_be_customized_by?(current_devise_api_user),
          canCustomizeHeaderLogo: can_customize_header_logo,
          canCustomizeColorScheme: can_customize_colors
        }

        # Include header/logo data if user has access or if data exists
        if can_customize_header_logo || header_logo_data?(conf)
          response.merge!({
                            customHeader: conf.custom_header,
                            headerFont: conf.header_font,
                            headerAlignment: conf.header_alignment,
                            headerFontSize: conf.header_font_size,
                            headerPlacement: conf.header_placement,
                            logoUrl: conf.logo_url,
                            logoPosition: conf.logo_position,
                            logoSize: conf.logo_size
                          })
        end

        # Include color scheme data if user has access or if data exists
        if can_customize_colors || color_scheme_data?(conf)
          response[:colorScheme] = {
            primary: conf.primary,
            primaryForeground: conf.primary_foreground,
            secondary: conf.secondary,
            secondaryForeground: conf.secondary_foreground,
            accent: conf.accent,
            accentForeground: conf.accent_foreground,
            highlight: conf.highlight,
            highlightForeground: conf.highlight_foreground,
            muted: conf.muted,
            mutedForeground: conf.muted_foreground,
            destructive: conf.destructive,
            destructiveForeground: conf.destructive_foreground,
            navbarBorder: conf.navbar_border
          }
        end

        response
      end

      def header_logo_data?(conf)
        [conf.custom_header, conf.header_font, conf.header_placement,
         conf.logo_url, conf.logo_position, conf.logo_size].any?(&:present?)
      end

      def color_scheme_data?(conf)
        [conf.primary, conf.primary_foreground, conf.secondary, conf.secondary_foreground,
         conf.accent, conf.accent_foreground, conf.highlight, conf.highlight_foreground,
         conf.muted, conf.muted_foreground, conf.destructive, conf.destructive_foreground].any?(&:present?)
      end
    end
  end
end
