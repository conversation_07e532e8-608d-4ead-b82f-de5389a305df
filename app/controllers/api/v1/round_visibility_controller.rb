module Api
  module V1
    class RoundVisibilityController < ApplicationController
      before_action :authenticate_devise_api_token!
      before_action :set_league
      before_action :authorize_owner!, except: [:visibility_info]
      before_action :require_content_creator!, except: [:visibility_info]

      # GET /api/v1/leagues/:id/round_visibility
      def index
        season_id = (params[:season_id].presence || @league.season_id).to_i
        lrvs = LeagueRoundVisibility.where(league_id: @league.id, season_id:).order(:round_number)
        data = lrvs.map do |lrv|
          serialize_visibility(lrv)[:data].merge(
            showcased_user_ids: lrv.showcased_users.pluck(:id)
          )
        end
        render json: { data: }
      end

      # PATCH /api/v1/leagues/:id/round_visibility_defaults
      def update_defaults
        setting = LeagueRoundVisibilitySetting.find_or_initialize_by(league: @league)
        dv = ActiveModel::Type::Boolean.new.cast(params[:default_visible_for_new_rounds])
        if params.key?(:default_visible_for_new_rounds).blank?
          render json: { error_key: 'NO_VISIBILITY_PARAMS' }, status: :bad_request and return
        end

        setting.default_visible_for_new_rounds = dv
        if setting.save
          render json: { data: { league_id: @league.id,
                                 default_visible_for_new_rounds:
                                 setting.default_visible_for_new_rounds } },
                 status: :ok
        else
          render json: { errors: setting.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # POST /api/v1/leagues/:id/rounds/:round_number/publish
      def publish
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || @league.season_id).to_i
        render json: { error_key: 'NO_SEASON_FOR_LEAGUE' }, status: :bad_request and return if season_id.zero?

        lrv = LeagueRoundVisibility.find_or_initialize_by(league: @league, season_id:, round_number:)
        # if lrv.persisted?
        #   # idempotent
        #   render json: serialize_visibility(lrv), status: :ok and return
        # end

        lrv.visible = true
        lrv.visible_at = Time.current
        lrv.made_visible_by_user = current_devise_api_user
        if lrv.save
          log_round_showcase(@league, round_number, 'published', {
                               user_id: current_devise_api_user.id,
                               season_id:
                             })
          render json: serialize_visibility(lrv), status: :ok
        else
          render json: { errors: lrv.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # POST /api/v1/leagues/:id/rounds/publish_bulk
      def publish_bulk
        season_id = (params[:season_id].presence || @league.season_id).to_i
        round_numbers = Array(params[:round_numbers]).map(&:to_i).uniq
        if season_id.zero? || round_numbers.empty?
          render json: { error_key: 'INVALID_PARAMS' }, status: :bad_request and return
        end

        created = []
        round_numbers.each do |rn|
          lrv = LeagueRoundVisibility.find_or_initialize_by(league: @league, season_id:, round_number: rn)
          next if lrv.persisted?

          lrv.visible = true
          lrv.visible_at = Time.current
          lrv.made_visible_by_user = current_devise_api_user
          created << lrv if lrv.save
        end

        render json: { data: created.map { |lrv| serialize_visibility(lrv)[:data] } }, status: :ok
      end

      # POST /api/v1/leagues/:id/rounds/:round_number/unpublish
      def unpublish
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || @league.season_id).to_i
        render json: { error_key: 'NO_SEASON_FOR_LEAGUE' }, status: :bad_request and return if season_id.zero?

        lrv = LeagueRoundVisibility.find_by(league: @league, season_id:, round_number:)
        return render json: { error_key: 'NOT_FOUND' }, status: :not_found unless lrv

        unless lrv.can_unpublish?
          return render json: { error_key: 'GRACE_PERIOD_EXPIRED' }, status: :unprocessable_entity
        end

        lrv.visible = false
        lrv.visible_at = nil
        lrv.made_visible_by_user = nil
        if lrv.save
          log_round_showcase(@league, round_number, 'unpublished', {
                               user_id: current_devise_api_user.id,
                               season_id:
                             })
          render json: serialize_visibility(lrv), status: :ok
        else
          render json: { errors: lrv.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # POST /api/v1/leagues/:id/rounds/:round_number/showcase_users
      def add_showcase_users
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || @league.season_id).to_i
        user_ids = Array(params[:user_ids]).map(&:to_i).uniq
        if season_id.zero? || user_ids.empty?
          return render json: { error_key: 'INVALID_PARAMS' },
                        status: :bad_request
        end
        # Find existing or create new LRV with proper defaults
        lrv = LeagueRoundVisibility.find_by(league: @league, season_id:, round_number:)

        lrv ||= LeagueRoundVisibility.create!(
          league: @league,
          season_id:,
          round_number:,
          visible: false # Explicitly set to false for showcasing
        )

        # Respect showcase_user_limit if set
        limit = @league.showcase_user_limit
        if limit && (lrv.showcased_users.count + user_ids.size) > limit
          return render json: { error_key: 'SHOWCASE_LIMIT_EXCEEDED', limit: }, status: :unprocessable_entity
        end

        created_ids = []
        user_ids.each do |uid|
          created = lrv.showcases.find_or_create_by(user_id: uid)
          created_ids << created.user_id if created.persisted?
        end

        render json: { data: { league_id: @league.id,
                               season_id:, round_number:,
                               showcased_user_ids: lrv.showcased_users.pluck(:id) } },
               status: :ok
      end

      # DELETE /api/v1/leagues/:id/rounds/:round_number/showcase_users/:user_id
      def remove_showcase_user
        round_number = params[:round_number].to_i
        season_id = (params[:season_id].presence || @league.season_id).to_i
        user_id = params[:user_id].to_i

        lrv = LeagueRoundVisibility.find_by(league: @league, season_id:, round_number:)
        return render json: { error_key: 'NOT_FOUND' }, status: :not_found unless lrv

        lrv.showcases.where(user_id:).delete_all

        render json: { data: { league_id: @league.id,
                               season_id:, round_number:,
                               showcased_user_ids: lrv.showcased_users.pluck(:id) } },
               status: :ok
      end

      # GET /api/v1/leagues/:id/rounds/visibility_info
      # Public to authenticated users; summarizes which rounds are published and what showcase data is available
      def visibility_info # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity,Metrics/MethodLength
        season_id = (params[:season_id].presence || @league.season_id).to_i
        return render json: { error_key: 'NO_SEASON_FOR_LEAGUE' }, status: :bad_request if season_id.zero?

        season = Season.find(season_id)

        # Handle cup vs league competitions differently
        round_numbers = if season.competition.competition_type == 'CUP'
                          get_cup_round_numbers(season)
                        else
                          get_league_round_numbers(season)
                        end

        lrvs = LeagueRoundVisibility.where(league_id: @league.id, season_id:)
        published_round_numbers = lrvs.where(visible: true).pluck(:round_number).uniq.sort

        unpublished_round_numbers = (round_numbers - published_round_numbers)

        has_league_showcased_users = LeagueUserShowcase.where(league_id: @league.id).exists?

        per_round = round_numbers.map do |rn|
          _matches, finished = if season.competition.competition_type == 'CUP'
                                 get_cup_round_matches_and_status(season, rn)
                               else
                                 get_league_round_matches_and_status(season, rn)
                               end

          is_published = published_round_numbers.include?(rn)
          lrv = lrvs.find { |l| l.round_number == rn }
          can_unpublish = lrv&.can_unpublish? || false

          # Check if round is currently ongoing (has live matches)
          is_ongoing = check_round_ongoing(season, rn)

          top_limit = auto_showcase_limit(@league, season_id, rn)
          {
            round_number: rn,
            visible: is_published,
            finished:,
            can_unpublish:,
            display_in_selector: should_display_in_selector(is_published, finished, is_ongoing, can_unpublish),
            top_performers_available: top_limit.positive?,
            league_showcased_users_available: has_league_showcased_users && finished,
            view_permission: if is_published
                               'all'
                             else
                               (if current_devise_api_user&.id == @league.owner_id
                                  top_limit.positive? || has_league_showcased_users ? 'owner_showcase' : 'owner'
                                else
                                  'self'
                                end)
                             end
          }
        end

        # Extract rounds with showcase data to avoid multi-line block chain
        rounds_with_showcase_data = per_round
                                    .select { |r| r[:top_performers_available] || r[:league_showcased_users_available] }
                                    .map { |r| r[:round_number] }

        render json: {
          data: {
            league_id: @league.id,
            season_id:,
            published_round_numbers:,
            unpublished_round_numbers:,
            rounds_with_showcase_data:,
            per_round:
          }
        }, status: :ok
      end

      private

      def set_league
        @league = League.find(params[:id])
      end

      def authorize_owner!
        return if current_devise_api_user.id == @league.owner_id

        render json: { error_key: 'LEAGUE_OWNER_ONLY' }, status: :unauthorized
      end

      def require_content_creator!
        return if current_devise_api_user.is_content_creator?

        render json: { error_key: 'NOT_CONTENT_CREATOR' }, status: :forbidden
      end

      def serialize_visibility(lrv)
        { data: { league_id: lrv.league_id, season_id: lrv.season_id, round_number: lrv.round_number,
                  visible: lrv.visible, visible_at: lrv.visible_at } }
      end

      # Returns the auto showcase limit (top-N) for a given round, mirroring RoundScoresController logic
      def auto_showcase_limit(league, season_id, round_number)
        return 0 unless league.scoring_visibility_mode == 'creator_controlled'

        # Must not be published
        published = LeagueRoundVisibility.find_by(league_id: league.id, season_id:,
                                                  round_number:, visible: true)
        return 0 if published

        # If default visibility is true, full scores are already visible
        setting = LeagueRoundVisibilitySetting.find_by(league_id: league.id)
        default_visible = setting.nil? ? true : setting.default_visible_for_new_rounds
        return 0 if default_visible

        limit = league.showcase_user_limit.to_i
        limit.positive? ? limit : 0
      end

      # Get round numbers for league competitions
      def get_league_round_numbers(season)
        season.matches.select(:matchday).distinct.pluck(:matchday).compact.sort
      end

      # Get round numbers for cup competitions (similar to MatchdayOptionsService)
      def get_cup_round_numbers(season)
        round_numbers = []
        overall_matchday = 0

        cup_stages_order.each do |stage|
          break if stage_is_future?(season, stage)

          stage_matchdays = season.matches.where(stage:).distinct.pluck(:matchday).sort
          current_stage_matchday = determine_current_matchday(season, stage)

          stage_matchdays.each do |matchday|
            next unless matchday_has_finished_or_ongoing_matches?(season, stage, matchday) ||
                        matchday == current_stage_matchday

            overall_matchday += 1
            round_numbers << overall_matchday
          end
        end

        round_numbers
      end

      # Get matches and finished status for league competitions
      def get_league_round_matches_and_status(season, round_number)
        matches = season.matches.where(matchday: round_number, stage: season.current_stage)
        finished = matches.present? && matches.all? { |m| %w[FINISHED POSTPONED CANCELLED].include?(m.status) }
        [matches, finished]
      end

      # Get matches and finished status for cup competitions
      def get_cup_round_matches_and_status(season, overall_round_number)
        # Convert overall round number back to stage and matchday
        stage, matchday = convert_overall_round_to_stage_matchday(season, overall_round_number)
        return [[], false] unless stage && matchday

        matches = season.matches.where(stage:, matchday:)
        finished = matches.present? && matches.all? { |m| %w[FINISHED POSTPONED CANCELLED].include?(m.status) }
        [matches, finished]
      end

      # Cup competition stages in order (similar to MatchdayOptionsService)
      def cup_stages_order
        %w[LEAGUE_STAGE PLAYOFFS LAST_16 QUARTER_FINALS SEMI_FINALS FINAL]
      end

      # Check if a stage is in the future
      def stage_is_future?(season, stage)
        current_stage_index = cup_stages_order.index(season.current_stage)
        stage_index = cup_stages_order.index(stage)
        return false unless current_stage_index && stage_index

        stage_index > current_stage_index
      end

      # Determine current matchday for a stage
      def determine_current_matchday(season, stage)
        matches = season.matches.where(stage:)
        ongoing_matches = matches.select { |match| %w[LIVE IN_PLAY PAUSED].include?(match.status) }

        return ongoing_matches.first.matchday if ongoing_matches.any?

        finished_matchdays = matches.select { |match| match.status == 'FINISHED' }
                                    .map(&:matchday).uniq.sort

        return finished_matchdays.last if finished_matchdays.any?

        scheduled_matchdays = matches.select { |match| %w[SCHEDULED TIMED].include?(match.status) }
                                     .map(&:matchday).uniq.sort

        scheduled_matchdays.first
      end

      # Check if matchday has finished or ongoing matches
      def matchday_has_finished_or_ongoing_matches?(season, stage, matchday)
        matches = season.matches.where(stage:, matchday:)
        return false if matches.empty?

        matches.any? { |match| match.status == 'FINISHED' || %w[LIVE IN_PLAY PAUSED].include?(match.status) }
      end

      # Convert overall round number back to stage and matchday for cup competitions
      def convert_overall_round_to_stage_matchday(season, overall_round_number)
        current_overall = 0

        cup_stages_order.each do |stage|
          break if stage_is_future?(season, stage)

          stage_matchdays = season.matches.where(stage:).distinct.pluck(:matchday).sort
          current_stage_matchday = determine_current_matchday(season, stage)

          stage_matchdays.each do |matchday|
            next unless matchday_has_finished_or_ongoing_matches?(season, stage, matchday) ||
                        matchday == current_stage_matchday

            current_overall += 1
            return [stage, matchday] if current_overall == overall_round_number
          end
        end

        [nil, nil]
      end

      # Check if a round is currently ongoing (has live matches)
      def check_round_ongoing(season, round_number)
        if season.competition.competition_type == 'CUP'
          stage, matchday = convert_overall_round_to_stage_matchday(season, round_number)
          return false unless stage && matchday

          matches = season.matches.where(stage:, matchday:)
        else
          matches = season.matches.where(matchday: round_number, stage: season.current_stage)
        end

        matches.any? { |match| %w[LIVE IN_PLAY PAUSED].include?(match.status) }
      end

      # Determine if round should be displayed in selector
      def should_display_in_selector(is_published, finished, is_ongoing, can_unpublish)
        # A round should be displayed when:
        # - The round is NOT yet published, AND
        # - Either the match status is FINISHED OR the round is currently ongoing, AND
        # - The round can still be unpublished (within grace period)
        !is_published && (finished || is_ongoing) && can_unpublish
      end
    end
  end
end
