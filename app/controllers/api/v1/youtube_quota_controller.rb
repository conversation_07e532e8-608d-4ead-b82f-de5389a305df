# app/controllers/api/v1/youtube_quota_controller.rb
module Api
  module V1
    class YoutubeQuotaController < ApplicationController
      before_action :authenticate_devise_api_token!

      def status
        quota_status = YoutubeQuotaStatus.current_status

        # Determine status message based on quota state
        status_message = if quota_status.quota_exhausted?
                           YoutubeQuotaStatus::TEMPORARILY_LIMITED
                         elsif quota_status.near_limit?
                           YoutubeQuotaStatus::HIGH_USAGE_WARNING
                         else
                           YoutubeQuotaStatus::FULLY_AVAILABLE
                         end

        # Simple response with only user-relevant information
        render json: {
          status: status_message,
          can_authenticate: !quota_status.quota_exhausted?,
          can_join_league: quota_status.can_accommodate?(1), # 1 unit for subscription check
          features_available: !quota_status.quota_exhausted?
        }, status: :ok
      end
    end
  end
end
