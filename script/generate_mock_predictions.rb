# Run this script with: rails runner script/generate_mock_predictions.rb

competition_id = 2
season_id = 2
user_ids = [10, 12]
matchdays = [4, 5]

# Score probability distribution (favoring lower numbers)
SCORE_WEIGHTS = {
  0 => 30,
  1 => 35,
  2 => 20,
  3 => 10,
  4 => 5
}

def weighted_random_score
  total = SCORE_WEIGHTS.values.sum
  rand_val = rand(total)
  cumulative = 0

  SCORE_WEIGHTS.each do |score, weight|
    cumulative += weight
    return score if rand_val < cumulative
  end

  0
end

puts 'Starting mock prediction generation...'
puts "Competition ID: #{competition_id}"
puts "Season ID: #{season_id}"
puts "Users: #{user_ids.join(', ')}"
puts "Matchdays: #{matchdays.join(', ')}"
puts '-' * 50

competition = Competition.find(competition_id)
season = Season.find(season_id)

matchdays.each do |matchday|
  puts "\nProcessing Matchday #{matchday}..."

  matches = Match.where(season_id:, matchday:, status: 'FINISHED')

  if matches.empty?
    puts "  ⚠️  No finished matches found for matchday #{matchday}"
    next
  end

  puts "  Found #{matches.count} finished matches"

  user_ids.each do |user_id|
    user = User.find(user_id)
    puts "\n  Creating predictions for #{user.username}..."

    # Create or find round prediction
    round_prediction = RoundPrediction.find_or_create_by!(
      user_id:,
      season_id:,
      competition_id:,
      matchday:,
      stage: 'REGULAR_SEASON'
    )

    matches.each do |match|
      # Skip if prediction already exists
      if MatchPrediction.exists?(round_prediction_id: round_prediction.id, match_id: match.id)
        puts "    ⏭️  Prediction already exists for Match #{match.id}"
        next
      end

      home_score = weighted_random_score
      away_score = weighted_random_score

      match_prediction = MatchPrediction.create!(
        round_prediction:,
        match:,
        user:,
        home_score:,
        away_score:
      )

      # Calculate points immediately
      match_prediction.calculate_points

      actual = "#{match.score&.fullTimeHome}-#{match.score&.fullTimeAway}"
      predicted = "#{home_score}-#{away_score}"
      points_emoji = case match_prediction.points
                     when 3 then '🎯'
                     when 1 then '✓'
                     when 0 then '✗'
                     else '?'
                     end

      puts "    #{points_emoji} Match #{match.id}: Predicted #{predicted} | Actual #{actual} | Points: #{match_prediction.points}"
    end

    # Recalculate round totals
    round_prediction.reload
    puts "\n  📊 Round #{matchday} Summary for #{user.username}:"
    puts "     Total Points: #{round_prediction.total_points}"
    puts "     Perfect (3pts): #{round_prediction.total_perfect}"
    puts "     Correct (1pt): #{round_prediction.total_correct}"
    puts "     Incorrect (0pts): #{round_prediction.zero_points_count}"
  end
end

puts "\n" + ('=' * 50)
puts 'Mock prediction generation complete!'
puts '=' * 50
