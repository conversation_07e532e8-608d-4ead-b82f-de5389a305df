# DEPRECATED: Use docs/api/schemas/round_visibility.yaml and docs/api/endpoints/standings_visibility.md instead.

openapi: 3.0.3
info:
  title: BragRights League Visibility API
  version: 1.0.0
  description: Controls for content creators to toggle league visibility of standings and round scores.
servers:
  - url: /api/v1
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    UpdateLeagueVisibilityRequest:
      type: object
      properties:
        standings_visible:
          type: boolean
          description: Whether league standings are visible to non-members/public
        round_scores_visible:
          type: boolean
          description: Whether round scores endpoints are visible to non-owners
      additionalProperties: false
    LeagueVisibility:
      type: object
      properties:
        id:
          type: integer
        standings_visible:
          type: boolean
        round_scores_visible:
          type: boolean
      required: [id, standings_visible, round_scores_visible]
    ApiError:
      type: object
      properties:
        error:
          type: string
        error_key:
          type: string
        message:
          type: string
paths:
  /leagues/{id}/visibility:
    patch:
      summary: Update visibility settings for a league
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
          description: League ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateLeagueVisibilityRequest"
            examples:
              example:
                value:
                  standings_visible: true
                  round_scores_visible: false
      responses:
        "200":
          description: Updated league visibility
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: "#/components/schemas/LeagueVisibility"
        "403":
          description: Not authorized (must be league owner and content creator)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiError"
              examples:
                not_owner:
                  value:
                    error_key: NOT_AUTHORIZED
                not_creator:
                  value:
                    error_key: NOT_CONTENT_CREATOR
        "404":
          description: League not found
        "422":
          description: Validation error
        "401":
          description: Unauthorized
