# Join Requests API

## Overview

The Join Requests API provides endpoints for managing requests to join leagues that require approval. These endpoints allow users to apply to join leagues, view their pending requests, and for league owners to approve or reject join requests.

## Endpoints

### Apply to Join a League

Submit a request to join a league that requires approval.

```
POST /api/v1/leagues/:id/join_requests
```

#### Parameters

- `:id` - The ID of the league to join

#### Authentication

Requires a valid access token.

#### Success Response (201 Created)

```json
{
  "data": {
    "id": 1,
    "league_id": 2,
    "user_id": 1,
    "status": "pending",
    "created_at": "2023-10-15T12:00:00.000Z",
    "updated_at": "2023-10-15T12:00:00.000Z",
    "league_name": "Office League",
    "competition_name": "Premier League"
  },
  "message": ["Join request submitted successfully"],
  "status": 201,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**Already a Member (422 Unprocessable Entity)**

```json
{
  "errors": ["You are already a member of this league"],
  "status": 422
}
```

**Request Already Exists (422 Unprocessable Entity)**

```json
{
  "errors": ["You already have a pending request for this league"],
  "status": 422
}
```

**Open League (422 Unprocessable Entity)**

```json
{
  "errors": ["This league is open and does not require approval to join"],
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Get League Join Requests

Retrieve all join requests for a league (league owner only).

```
GET /api/v1/leagues/:id/join_requests
```

#### Parameters

- `:id` - The ID of the league

#### Authentication

Requires a valid access token from the league owner.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "league_id": 2,
      "user_id": 1,
      "status": "pending",
      "created_at": "2023-10-15T12:00:00.000Z",
      "updated_at": "2023-10-15T12:00:00.000Z",
      "user_username": "user123"
    },
    {
      "id": 2,
      "league_id": 2,
      "user_id": 3,
      "status": "pending",
      "created_at": "2023-10-16T12:00:00.000Z",
      "updated_at": "2023-10-16T12:00:00.000Z",
      "user_username": "friend1"
    }
  ],
  "message": ["Join requests found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["You must be the league owner to view join requests"],
  "status": 403
}
```

### Get User's Join Requests

Retrieve all join requests made by the current user.

```
GET /api/v1/join_requests/user_requests
```

#### Authentication

Requires a valid access token.

#### Success Response (200 OK)

```json
{
  "data": [
    {
      "id": 1,
      "league_id": 2,
      "user_id": 1,
      "status": "pending",
      "created_at": "2023-10-15T12:00:00.000Z",
      "updated_at": "2023-10-15T12:00:00.000Z",
      "league_name": "Office League",
      "competition_name": "Premier League"
    },
    {
      "id": 3,
      "league_id": 4,
      "user_id": 1,
      "status": "approved",
      "created_at": "2023-10-14T12:00:00.000Z",
      "updated_at": "2023-10-14T14:00:00.000Z",
      "league_name": "Family League",
      "competition_name": "Champions League"
    }
  ],
  "message": ["Join requests found"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

### Update Join Request Status

Approve or reject a join request (league owner only).

```
PUT /api/v1/leagues/:id/join_requests/:request_id
```

#### Parameters

- `:id` - The ID of the league
- `:request_id` - The ID of the join request

#### Request Body

```json
{
  "status": "approved" // or "rejected"
}
```

#### Authentication

Requires a valid access token from the league owner.

#### Success Response (200 OK)

```json
{
  "data": {
    "id": 1,
    "league_id": 2,
    "user_id": 1,
    "status": "approved",
    "created_at": "2023-10-15T12:00:00.000Z",
    "updated_at": "2023-10-16T12:00:00.000Z",
    "user_username": "user123"
  },
  "message": ["Join request approved"],
  "status": 200,
  "type": "success"
}
```

#### Error Responses

**League Not Found (404 Not Found)**

```json
{
  "errors": ["League not found"],
  "status": 404
}
```

**Join Request Not Found (404 Not Found)**

```json
{
  "errors": ["Join request not found"],
  "status": 404
}
```

**Invalid Status (422 Unprocessable Entity)**

```json
{
  "errors": ["Status must be 'approved' or 'rejected'"],
  "status": 422
}
```

**Already Processed (422 Unprocessable Entity)**

```json
{
  "errors": ["This join request has already been processed"],
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["You must be the league owner to update join requests"],
  "status": 403
}
```

## Implementation Notes

1. **Join Request Status**: Join requests can have one of three statuses: "pending", "approved", or "rejected".
2. **Automatic Membership**: When a join request is approved, a league membership is automatically created for the user.
3. **League Types**: Only leagues with `open: false` require join requests. Open leagues can be joined directly.
4. **Request Uniqueness**: A user can only have one pending request per league at a time.
5. **Request History**: Approved and rejected requests are kept in the system for historical purposes.

## Frontend Integration

The frontend should:

1. Provide a button to apply to join closed leagues
2. Display a list of the user's pending join requests
3. For league owners, show a list of pending requests with approve/reject buttons
4. Update the UI appropriately when a request is approved or rejected
5. Disable the "Apply to Join" button if the user already has a pending request
