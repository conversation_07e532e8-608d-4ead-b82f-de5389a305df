openapi: 3.0.3
info:
  title: BragRights YouTube API
  description: Complete API documentation for YouTube integration including quota management
  version: 1.0.0

paths:
  # Public YouTube Endpoints
  /api/v1/youtube_auth/status:
    get:
      summary: Get YouTube connection status
      description: Returns YouTube connection status with simplified quota information
      tags:
        - YouTube Auth
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Status retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/youTubeAuthStatus"

  /api/v1/youtube_quota/status:
    get:
      summary: Get YouTube quota status
      description: Returns simplified quota status for regular users
      tags:
        - YouTube Quota
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Quota status retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicQuotaStatus"

  # Admin YouTube Endpoints
  /api/v1/admin/youtube/quota/detailed_status:
    get:
      summary: Get detailed quota status (Admin only)
      description: Returns comprehensive quota information including usage metrics and history
      tags:
        - Admin YouTube
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Detailed status retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminQuotaStatus"
        "403":
          description: Admin access required

  /api/v1/admin/youtube/quota/reset:
    post:
      summary: Reset quota usage (Admin only)
      description: Resets the current day's quota usage to zero
      tags:
        - Admin YouTube
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Quota reset successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Quota reset successfully"

  /api/v1/admin/youtube/quota/force_exhaustion:
    post:
      summary: Force quota exhaustion (Admin only)
      description: Manually marks quota as exhausted for testing purposes
      tags:
        - Admin YouTube
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Quota marked as exhausted
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Quota marked as exhausted"

components:
  schemas:
    PublicQuotaStatus:
      type: object
      required:
        - status
        - can_authenticate
        - can_join_league
        - features_available
      properties:
        status:
          type: string
          enum:
            - temporarily_limited
            - high_usage_warning
            - fully_available
          description: User-friendly status key for frontend localization
        can_authenticate:
          type: boolean
          description: Whether user can perform YouTube authentication
        can_join_league:
          type: boolean
          description: Whether user can join YouTube subscriber leagues
        features_available:
          type: boolean
          description: Whether YouTube features are generally available

    YouTubeAuthStatus:
      type: object
      required:
        - connected
        - is_content_creator
        - has_basic_scope
        - has_youtube_scope
        - needs_scope_upgrade
        - can_use_youtube_features
        - quota_status
      properties:
        connected:
          type: boolean
        is_content_creator:
          type: boolean
        channel_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
        avatar_url:
          type: string
          nullable: true
        subscriber_count:
          type: integer
          nullable: true
        verified_at:
          type: string
          format: date-time
          nullable: true
        has_basic_scope:
          type: boolean
        has_youtube_scope:
          type: boolean
        needs_scope_upgrade:
          type: boolean
        can_use_youtube_features:
          type: boolean
        quota_status:
          $ref: "#/components/schemas/PublicQuotaStatus"

    AdminQuotaStatus:
      type: object
      required:
        - current_status
        - recent_history
      properties:
        current_status:
          type: object
          properties:
            usage_percentage:
              type: number
              format: float
            current_usage:
              type: integer
            daily_limit:
              type: integer
            remaining_quota:
              type: integer
            buffer_threshold:
              type: integer
            exhausted:
              type: boolean
            exhausted_at:
              type: string
              format: date-time
              nullable: true
            exhaustion_reason:
              type: string
              nullable: true
            near_limit:
              type: boolean
        recent_history:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              usage:
                type: integer
              limit:
                type: integer
              usage_percentage:
                type: number
                format: float
              exhausted:
                type: boolean
              exhausted_at:
                type: string
                format: date-time
                nullable: true
              exhaustion_reason:
                type: string
                nullable: true

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
