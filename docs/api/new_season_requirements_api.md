# New Season Requirements API

## Overview

The New Season Requirements API provides endpoints for retrieving information about what parameters are required to create a new season for a specific competition, and for creating a new season. These endpoints are essential for the frontend to properly implement the "Create New Season" functionality.

## Endpoints

### Get Competition Requirements

Retrieve information about what parameters are required to create a new season for a specific competition.

```
GET /api/v1/admin/competitions/:id/new_season_requirements
```

#### Parameters

- `:id` - The ID or code of the competition (e.g., `1` or `PL`)

#### Authentication

Requires a valid access token from an admin user.

#### Success Response (200 OK)

For FootballData competitions (PL, CL):

```json
{
  "competition": {
    "id": 123,
    "name": "Premier League",
    "code": "PL",
    "source": "football_data",
    "external_service_id": "2021"
  },
  "requirements": {
    "needs_season_external_service_id": false,
    "season_id_format": null
  }
}
```

For ApiFootball competitions (SV):

```json
{
  "competition": {
    "id": 124,
    "name": "Swedish Superliga",
    "code": "SV",
    "source": "api_football",
    "external_service_id": "113"
  },
  "requirements": {
    "needs_season_external_service_id": true,
    "season_id_format": "year",
    "season_id_example": "2026"
  }
}
```

For KSI competitions (BDkk):

```json
{
  "competition": {
    "id": 125,
    "name": "Icelandic Cup",
    "code": "BDkk",
    "source": "ksi_soap",
    "external_service_id": "1"
  },
  "requirements": {
    "needs_season_external_service_id": true,
    "season_id_format": "numeric_id",
    "season_id_example": "50000"
  }
}
```

#### Error Responses

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["Only admin can manage competitions"],
  "status": 403
}
```

### Create New Season

Create a new season for a competition.

```
POST /api/v1/admin/competitions/:id/create_new_season
```

#### Parameters

- `:id` - The ID or code of the competition (e.g., `1` or `PL`)

#### Request Body

The request body depends on the competition source:

For FootballData competitions (PL, CL):

```json
{}
```

For ApiFootball competitions (SV):

```json
{
  "season_external_service_id": "2026"
}
```

For KSI competitions (BDkk):

```json
{
  "season_external_service_id": "50000"
}
```

#### Authentication

Requires a valid access token from an admin user.

#### Success Response (200 OK)

```json
{
  "message": "New season created successfully for Competition Name",
  "season": {
    "id": 123,
    "start_date": "2023-08-01",
    "end_date": "2024-05-31",
    "current_matchday": 1,
    "external_service_id": "2023",
    "source": "football_data"
  }
}
```

#### Error Responses

**Competition Not Found (404 Not Found)**

```json
{
  "errors": ["Competition not found"],
  "status": 404
}
```

**Missing Required Parameters (400 Bad Request)**

```json
{
  "errors": ["Season external service ID is required for this competition"],
  "status": 400
}
```

**External Service Error (422 Unprocessable Entity)**

```json
{
  "errors": ["Failed to fetch data from external service: API rate limit exceeded"],
  "status": 422
}
```

**Unauthorized (401 Unauthorized)**

```json
{
  "errors": ["You need to sign in or sign up before continuing."],
  "status": 401
}
```

**Forbidden (403 Forbidden)**

```json
{
  "errors": ["Only admin can manage competitions"],
  "status": 403
}
```

## Implementation Notes

1. **Competition Sources**: Different competition sources have different requirements for creating a new season:
   - `football_data`: No additional parameters required
   - `api_football`: Requires `season_external_service_id` in year format (e.g., "2026")
   - `ksi_soap`: Requires `season_external_service_id` in numeric ID format (e.g., "50000")

2. **Season Transition**: Creating a new season will:
   - Archive the current season
   - Create a new season with data from the external service
   - Set it as the current season for the competition
   - Transition all leagues in the competition to use the new season

3. **Admin Only**: These endpoints are only accessible to users with admin privileges.

4. **Rate Limiting**: Be mindful of external API rate limits when creating new seasons.

## Frontend Integration

The frontend should:

1. Call the `/api/v1/admin/competitions/:id/new_season_requirements` endpoint to determine what parameters are needed for the specific competition.

2. Display a form based on the requirements:
   - If `needs_season_external_service_id` is true, show a field for the season external service ID.
   - Use `season_id_format` and `season_id_example` to provide guidance to the user.

3. Submit the create new season request:
   - Call the `/api/v1/admin/competitions/:id/create_new_season` endpoint with the appropriate parameters.
   - Include `season_external_service_id` if required by the competition.

4. Handle the response:
   - Display success message if the season was created successfully.
   - Display appropriate error message if the request failed.
   - Refresh the competition data to show the new season.
