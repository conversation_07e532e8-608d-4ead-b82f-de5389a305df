openapi: 3.0.3
info:
  title: YouTube Quota API
  description: API for monitoring YouTube API quota usage and status
  version: 1.0.0

paths:
  /api/v1/youtube_quota/quota_status:
    get:
      summary: Get current YouTube API quota status
      description: Returns the current quota usage and availability status for YouTube API calls
      tags:
        - YouTube Quota
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Quota status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuotaStatus'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/v1/youtube_auth/status:
    get:
      summary: Get YouTube connection status with quota information
      description: Returns YouTube connection status including quota information
      tags:
        - YouTube Auth
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/YouTubeAuthStatus'

  /api/v1/youtube_quota/detailed_status:
    get:
      summary: Get detailed quota status (Admin only)
      description: Returns detailed quota information including historical data
      tags:
        - YouTube Quota
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Detailed status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedQuotaStatus'
        '403':
          description: Admin access required

components:
  schemas:
    QuotaStatus:
      type: object
      required:
        - can_authenticate
        - is_limited
        - usage_percentage
        - current_usage
        - daily_limit
        - remaining_quota
        - message
      properties:
        can_authenticate:
          type: boolean
          description: Whether authentication operations are allowed
        is_limited:
          type: boolean
          description: Whether quota usage is limited or exhausted
        usage_percentage:
          type: number
          format: float
          minimum: 0
          maximum: 100
          description: Percentage of daily quota used
        current_usage:
          type: integer
          minimum: 0
          description: Current quota units used today
        daily_limit:
          type: integer
          minimum: 1
          description: Daily quota limit
        remaining_quota:
          type: integer
          minimum: 0
          description: Remaining quota units for today
        message:
          type: string
          enum:
            - temporarily_limited
            - high_usage_warning
            - fully_available
          description: Status message indicating quota availability

    YouTubeAuthStatus:
      type: object
      required:
        - connected
        - is_content_creator
        - has_basic_scope
        - has_youtube_scope
        - needs_scope_upgrade
        - can_use_youtube_features
        - quota_status
      properties:
        connected:
          type: boolean
        is_content_creator:
          type: boolean
        channel_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
        avatar_url:
          type: string
          nullable: true
        subscriber_count:
          type: integer
          nullable: true
        verified_at:
          type: string
          format: date-time
          nullable: true
        has_basic_scope:
          type: boolean
        has_youtube_scope:
          type: boolean
        needs_scope_upgrade:
          type: boolean
        can_use_youtube_features:
          type: boolean
        quota_status:
          $ref: '#/components/schemas/QuotaStatus'

    DetailedQuotaStatus:
      type: object
      required:
        - current_status
        - recent_history
      properties:
        current_status:
          $ref: '#/components/schemas/QuotaStatus'
        recent_history:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              usage:
                type: integer
              limit:
                type: integer
              usage_percentage:
                type: number
                format: float
              exhausted:
                type: boolean
              exhausted_at:
                type: string
                format: date-time
                nullable: true
              exhaustion_reason:
                type: string
                nullable: true

    Error:
      type: object
      required:
        - error
      properties:
        error:
          type: string
          description: Error message

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT