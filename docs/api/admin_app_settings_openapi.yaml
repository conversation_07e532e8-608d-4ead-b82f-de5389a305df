openapi: 3.0.3
info:
  title: Admin AppSettings API
  version: 1.0.0
  description: CRUD endpoints for managing application settings, including league join limits
servers:
  - url: http://api.bragrights.football
    description: Production API server
paths:
  /api/v1/admin/app_settings:
    get:
      summary: List all app settings
      tags: [Admin, AppSettings]
      security: [{ BearerAuth: [] }]
      responses:
        '200':
          description: A list of settings
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AppSetting'
    post:
      summary: Create or update an app setting (upsert)
      tags: [Admin, AppSettings]
      security: [{ BearerAuth: [] }]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppSettingWrite'
            example:
              key: default_league_join_limit
              value: "12"
      responses:
        '201':
          description: Setting created or updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppSetting'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /api/v1/admin/app_settings/{key}:
    parameters:
      - in: path
        name: key
        schema: { type: string }
        required: true
        description: Setting key
    get:
      summary: Get a specific setting
      tags: [Admin, AppSettings]
      security: [{ BearerAuth: [] }]
      responses:
        '200':
          description: Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppSetting'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    patch:
      summary: Update or create a specific setting (upsert)
      tags: [Admin, AppSettings]
      security: [{ BearerAuth: [] }]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                value:
                  type: string
              required: [value]
            example:
              value: "15"
      responses:
        '200':
          description: Updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppSetting'
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppSetting'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    AppSetting:
      type: object
      required: [key, value]
      properties:
        key:
          type: string
          description: Setting key
          example: default_league_join_limit
        value:
          type: string
          description: Stored string value (validated per key)
          example: "12"
    AppSettingWrite:
      type: object
      required: [key, value]
      properties:
        key:
          type: string
        value:
          type: string
    Error:
      type: object
      properties:
        error:
          type: string
          example: validation_error
        message:
          type: string
          example: value must be an integer >= 1 for key default_league_join_limit
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

