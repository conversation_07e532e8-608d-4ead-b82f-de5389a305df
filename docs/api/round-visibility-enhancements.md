# Round Visibility API Enhancements

## Overview

This document describes the enhancements made to the Round Visibility API to support:
1. Cup competition handling
2. Grace period for unpublishing rounds
3. Enhanced visibility_info endpoint response

## New Endpoints

### POST /api/v1/leagues/:id/rounds/:round_number/unpublish

Unpublishes a round within the 1-day grace period after publishing.

**Authentication**: Required (league owner + content creator)

**Parameters**:
- `id` (path): League ID
- `round_number` (path): Round number to unpublish
- `season_id` (body, optional): Season ID (defaults to league's current season)

**Request Body**:
```json
{
  "season_id": 123
}
```

**Responses**:

200 OK - Round successfully unpublished:
```json
{
  "data": {
    "league_id": 1,
    "season_id": 123,
    "round_number": 5,
    "visible": false,
    "visible_at": null
  }
}
```

404 Not Found - Round visibility record not found:
```json
{
  "error_key": "NOT_FOUND"
}
```

422 Unprocessable Entity - Grace period expired:
```json
{
  "error_key": "GRACE_PERIOD_EXPIRED"
}
```

## Enhanced Endpoints

### GET /api/v1/leagues/:id/rounds/visibility_info

The visibility_info endpoint now includes additional fields in the `per_round` array:

**New Fields**:
- `can_unpublish` (boolean): Whether the round can currently be unpublished (within 1-day grace period)
- `display_in_selector` (boolean): Whether the round should be shown in the frontend round selector

**Enhanced Response**:
```json
{
  "data": {
    "league_id": 1,
    "season_id": 123,
    "published_round_numbers": [1, 2, 3],
    "unpublished_round_numbers": [4, 5, 6],
    "rounds_with_showcase_data": [4, 5],
    "per_round": [
      {
        "round_number": 1,
        "visible": true,
        "finished": true,
        "can_unpublish": false,
        "display_in_selector": false,
        "top_performers_available": false,
        "league_showcased_users_available": true,
        "view_permission": "all"
      },
      {
        "round_number": 4,
        "visible": false,
        "finished": true,
        "can_unpublish": true,
        "display_in_selector": true,
        "top_performers_available": true,
        "league_showcased_users_available": true,
        "view_permission": "owner_showcase"
      }
    ]
  }
}
```

**Field Descriptions**:

- `can_unpublish`: `true` if the round is published and within the 1-day grace period, `false` otherwise
- `display_in_selector`: `true` when:
  - The round is NOT yet published, AND
  - Either the match status is FINISHED OR the round is currently ongoing, AND
  - The round can still be unpublished (within grace period)

## Cup Competition Support

The visibility_info endpoint now properly handles cup competitions by:

1. **Overall Round Numbering**: Cup competitions use overall round numbers that span across different stages (LEAGUE_STAGE, PLAYOFFS, LAST_16, etc.)

2. **Stage-Aware Matching**: Matches are filtered by both stage and matchday for accurate status determination

3. **Progressive Stage Handling**: Only includes rounds from current and past stages, not future stages

**Cup Stages Order**:
- LEAGUE_STAGE
- PLAYOFFS
- LAST_16
- QUARTER_FINALS
- SEMI_FINALS
- FINAL

## Grace Period Implementation

### Model Changes

The `LeagueRoundVisibility` model now includes:

- `can_unpublish?` method: Checks if unpublishing is allowed (within 1-day grace period)
- Enhanced validation: Prevents unpublishing after grace period expires

### Grace Period Logic

- Grace period: 24 hours from `visible_at` timestamp
- Unpublishing allowed: `Time.current <= visible_at + 1.day`
- After grace period: Unpublishing is permanently disabled

## Error Handling

### New Error Keys

- `GRACE_PERIOD_EXPIRED`: Returned when attempting to unpublish after the 1-day grace period
- `NOT_FOUND`: Returned when the round visibility record doesn't exist

### Existing Error Keys

- `NO_SEASON_FOR_LEAGUE`: No valid season ID provided
- `LEAGUE_OWNER_ONLY`: User is not the league owner
- `NOT_CONTENT_CREATOR`: User is not a content creator

## Logging

All round visibility operations are now logged with custom loggers:

### Log Categories

- **Round Showcase**: Publish/unpublish actions, showcase user changes
- **Match Operations**: Match state changes, score updates
- **Predictions**: Point calculations, round aggregations
- **Background Jobs**: Job execution, failures, retries
- **League Customization**: League settings changes

### Log Format

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "action": "published",
  "league_id": 1,
  "league_name": "Premier League Predictions",
  "season_id": 123,
  "round_number": 5,
  "user_id": 42,
  "details": {}
}
```

## Frontend Integration

### Round Selector Logic

Use the `display_in_selector` field to determine which rounds to show in the frontend round selector:

```typescript
const selectableRounds = visibilityInfo.per_round
  .filter(round => round.display_in_selector)
  .map(round => round.round_number);
```

### Unpublish Button Logic

Show unpublish button only when `can_unpublish` is `true`:

```typescript
const canUnpublish = roundInfo.visible && roundInfo.can_unpublish;
```

## Testing Considerations

### Test Cases to Add

1. **Cup Competition Handling**:
   - Test overall round numbering across stages
   - Test stage progression and future stage filtering
   - Test match status determination for cup rounds

2. **Grace Period Functionality**:
   - Test unpublishing within grace period (should succeed)
   - Test unpublishing after grace period (should fail)
   - Test grace period calculation edge cases

3. **Enhanced visibility_info Response**:
   - Test `can_unpublish` field accuracy
   - Test `display_in_selector` field logic
   - Test response format consistency

### Mock Data Requirements

- Cup competition with multiple stages
- Published rounds with various `visible_at` timestamps
- Mix of finished, ongoing, and scheduled matches
