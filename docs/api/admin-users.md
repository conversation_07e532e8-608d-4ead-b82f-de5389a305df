# Admin Users API

Base: /api/v1/admin/users

Auth: Bearer token, admin role required.

## GET /api/v1/admin/users

Query params:
- email?: string (ILIKE filter)
- username?: string (ILIKE filter)
- registered_after?: ISO8601
- registered_before?: ISO8601
- page?: number
- per_page?: number

200 OK
```json
{
  "data": [
    {"id":1,"email":"<EMAIL>","username":"alice","admin":false,"created_at":"2025-05-01T12:00:00Z","youtube_channel_id":null,"youtube_channel_name":null,"is_content_creator":false,"youtube_subscriber_count":0}
  ],
  "pagination": {"page":1,"per_page":25,"total_pages":1,"total_count":1}
}
```

401/403 on auth failure.

## GET /api/v1/admin/users/:id

200 OK
```json
{
  "data": {"id":1,"email":"<EMAIL>","username":"alice","admin":false,"created_at":"2025-05-01T12:00:00Z","updated_at":"2025-05-01T12:30:00Z","youtube_channel_id":null,"youtube_channel_name":null,"is_content_creator":false,"youtube_subscriber_count":0},
  "memberships": [{"league_id":42,"league_name":"My League","joined_at":"2025-05-10T19:00:00Z"}],
  "predictions_count": 123
}
```

## PATCH /api/v1/admin/users/:id

Body:
```json
{ "user": {"email":"<EMAIL>","username":"newname","admin":true} }
```

200 OK
```json
{ "data": {"id":1,"email":"<EMAIL>","username":"newname","admin":true} }
```

422 on validation errors.

## DELETE /api/v1/admin/users/:id

Soft delete.

200 OK
```json
{ "message": "User soft-deleted" }
```

## POST /api/v1/admin/users/:id/restore

200 OK
```json
{ "message": "User restored" }
```

## TypeScript Contracts

```ts
export interface AdminUser {
  id: number;
  email: string;
  username: string;
  admin: boolean;
  created_at?: string;
  updated_at?: string;
  youtube_channel_id?: string | null;
  youtube_channel_name?: string | null;
  is_content_creator?: boolean;
  youtube_subscriber_count?: number | null;
}

export interface AdminUsersIndexResponse {
  data: AdminUser[];
  pagination: { page?: number; per_page?: number; total_pages?: number; total_count?: number };
}

export interface AdminUserShowResponse {
  data: AdminUser;
  memberships: { league_id: number; league_name: string; joined_at: string }[];
  predictions_count: number;
}

export interface AdminUserUpdateRequest { user: Partial<Pick<AdminUser, 'email'|'username'|'admin'>> }
export interface AdminUserUpdateResponse { data: Pick<AdminUser, 'id'|'email'|'username'|'admin'> }
```

## curl Examples

```bash
curl -H "Authorization: Bearer $ADMIN_TOKEN" "$API_URL/api/v1/admin/users?email=alice"

curl -H "Authorization: Bearer $ADMIN_TOKEN" "$API_URL/api/v1/admin/users/1"

curl -X PATCH -H "Authorization: Bearer $ADMIN_TOKEN" -H "Content-Type: application/json" \
  -d '{"user":{"admin":true}}' \
  "$API_URL/api/v1/admin/users/1"

curl -X DELETE -H "Authorization: Bearer $ADMIN_TOKEN" "$API_URL/api/v1/admin/users/1"

curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" "$API_URL/api/v1/admin/users/1/restore"
```

