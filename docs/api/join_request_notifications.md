# Join Request Notifications

## Overview

The Join Request Notifications feature alerts league owners when users apply to join their private leagues. This documentation outlines how notifications are implemented and how they can be customized.

## Implementation Details

When a user submits a request to join a private league, the system automatically:

1. Creates a join request record in the database
2. Sends an email notification to the league owner
3. (Optional) Creates an in-app notification if the notification system is enabled

## Email Notifications

### Triggering

Email notifications are sent automatically when:
- A new join request is created
- The status of a join request changes (if configured)

### Content

The email notification includes:
- The username of the requesting user
- The name of the league
- A link to the league management page where the owner can approve/reject requests

### Configuration

Email notifications can be configured in the application settings:
- Enable/disable email notifications
- Customize email templates
- Set notification frequency (immediate or digest)

## In-App Notifications

If the application has an in-app notification system, join request notifications will appear in the league owner's notification center, showing:
- The user who requested to join
- The league name
- When the request was made
- A direct link to approve/reject the request

## Frontend Integration

The frontend should:
1. Display a notification badge on the league management section when new requests exist
2. Provide a dedicated area to view and manage join requests
3. Allow owners to approve or reject requests directly from the notification
4. Mark notifications as read when viewed

## Technical Implementation

The notification system is implemented in the `LeagueJoinRequestsController` and uses:
- The `LeagueJoinRequestMailer` for email notifications
- (Optional) A notification model for in-app notifications

### Implementation Code

#### Controller Modification

```ruby
# app/controllers/api/v1/league_join_requests_controller.rb
def create
  return render_already_member if @league.users.include?(current_devise_api_user)
  return render_maximum_leagues if current_devise_api_user.memberships.count >= 5

  # Check if this is a subscriber-only league
  if @league.subscriber_only_league? && !@league.user_meets_subscription_requirements?(current_devise_api_user)
    render json: {
      error: 'This league is only available to YouTube subscribers of the channel',
      youtube_channel_id: @league.youtube_channel_id
    }, status: :forbidden
    return
  end

  request = @league.league_join_requests.new(user: current_devise_api_user)

  if request.save
    # Notify the league owner about the new join request
    notify_league_owner(request)
    
    render json: { message: 'Join request submitted successfully' },
           status: :created
  else
    render json: { errors: request.errors.full_messages },
           status: :unprocessable_entity
  end
end

private

# Add this new method to notify the league owner
def notify_league_owner(request)
  # You could implement email notifications here
  LeagueJoinRequestMailer.new_request_notification(request).deliver_later
  
  # Or create an in-app notification record if you have a notifications system
  # Notification.create(
  #   recipient: @league.owner,
  #   actor: current_devise_api_user,
  #   action: 'requested to join',
  #   notifiable: request
  # )
end
```

#### Mailer Implementation

```ruby
# app/mailers/league_join_request_mailer.rb
class LeagueJoinRequestMailer < ApplicationMailer
  def new_request_notification(request)
    @request = request
    @league = request.league
    @user = request.user
    @owner = @league.owner
    
    mail(
      to: @owner.email,
      subject: "New join request for #{@league.name}"
    )
  end
end
```

#### Email Template

```erb
<!-- app/views/league_join_request_mailer/new_request_notification.html.erb -->
<h1>New Join Request</h1>

<p>Hello <%= @owner.username %>,</p>

<p>User <strong><%= @user.username %></strong> has requested to join your league <strong><%= @league.name %></strong>.</p>

<p>You can review and manage join requests by visiting your league management page.</p>

<p>Thank you for using <%= app_name %>!</p>
```

## Future Enhancements

Planned enhancements include:
1. Push notifications for mobile users
2. Notification preferences per league
3. Batch approval/rejection of multiple requests
4. Notification analytics to track response times