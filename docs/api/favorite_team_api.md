# Favorite Team API Integration Guide

This API allows users to manage their favorite teams in different competitions. Each user can have one favorite team per competition.

> **Related API**: See the [Team Standings API](./team_standings_api.md) for retrieving standings of users who have selected a specific team as their favorite.

## Endpoints

### List Favorite Teams

Get all favorite teams for the authenticated user.

```
GET /api/v1/favorite_teams
```

#### Response

```json
[
  {
    "id": 1,
    "team": {
      "id": 123,
      "name": "Manchester United",
      "short_name": "Man United",
      "tla": "M<PERSON>",
      "crest_public_id": "teams/crests/man_utd_logo"
    },
    "competition": {
      "id": 39,
      "name": "Premier League",
      "emblem_public_id": "competitions/emblems/premier_league"
    }
  }
]
```

### Get Favorite Team for Competition

Get the authenticated user's favorite team for a specific competition.

```
GET /api/v1/competitions/:competition_id/favorite_team
```

#### Response

If a favorite team exists:

```json
{
  "id": 1,
  "team": {
    "id": 123,
    "name": "Manchester United",
    "short_name": "Man United",
    "tla": "<PERSON><PERSON>",
    "crest_public_id": "teams/crests/man_utd_logo"
  },
  "competition": {
    "id": 39,
    "name": "Premier League",
    "emblem_public_id": "competitions/emblems/premier_league"
  }
}
```

If no favorite team exists:

```json
{
  "favorite_team": null
}
```

### Set Favorite Team

Set or update the authenticated user's favorite team for a competition.

```
POST /api/v1/favorite_teams
```

#### Parameters

| Name           | Type    | Description                                   |
| -------------- | ------- | --------------------------------------------- |
| team_id        | integer | The ID of the team to set as favorite         |
| competition_id | integer | The ID of the competition the team belongs to |

#### Response

```json
{
  "message": "Manchester United has been set as your favorite team for Premier League"
}
```

### Remove Favorite Team

Remove a favorite team.

```
DELETE /api/v1/favorite_teams/:id
```

#### Response

Success:

```json
{
  "message": "Favorite team removed successfully"
}
```

Not Found:

```json
{
  "error": "No favorite team found with this ID"
}
```

Unauthorized:

```json
{
  "error": "You are not authorized to perform this action"
}
```

## Integration Example

Here's an example of how to integrate with the Favorite Team API using TypeScript and axios:

```typescript
interface Team {
  id: number;
  name: string;
  short_name: string;
  tla: string;
  crest_public_id: string;
}

interface Competition {
  id: number;
  name: string;
  emblem_public_id: string;
}

interface FavoriteTeam {
  id: number;
  team: Team;
  competition: Competition;
}

// Get all favorite teams
const getFavoriteTeams = async (): Promise<FavoriteTeam[]> => {
  const response = await axios.get("/api/v1/favorite_teams");
  return response.data;
};

// Get favorite team for a competition
const getFavoriteTeamForCompetition = async (
  competitionId: number
): Promise<FavoriteTeam | null> => {
  const response = await axios.get(
    `/api/v1/competitions/${competitionId}/favorite_team`
  );
  return response.data.favorite_team;
};

// Set favorite team
const setFavoriteTeam = async (
  teamId: number,
  competitionId: number
): Promise<void> => {
  await axios.post("/api/v1/favorite_teams", {
    team_id: teamId,
    competition_id: competitionId,
  });
};

// Remove favorite team
const removeFavoriteTeam = async (favoriteTeamId: number): Promise<void> => {
  await axios.delete(`/api/v1/favorite_teams/${favoriteTeamId}`);
};
```

## Error Handling

The API uses standard HTTP status codes:

- 200: Success
- 201: Created (when setting a favorite team)
- 401: Unauthorized (not authenticated or trying to modify another user's favorite team)
- 404: Not Found (favorite team doesn't exist)
- 422: Unprocessable Entity (validation errors)

Error responses include a message or array of error messages:

```json
{
  "errors": ["You can only have one favorite team per competition"]
}
```

## Authentication

All endpoints require authentication. Include the authentication token in the request headers:

```typescript
axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
```

The API will return a 401 Unauthorized status code if the request is not properly authenticated.
