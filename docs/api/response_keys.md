# API Response Keys for Frontend Messaging

This document lists the standardized response keys returned by the backend for success and error handling, so the frontend can map them to localized user-visible messages. For each key, we also note any additional fields included in the response payload that the FE may need.

Note: Keys are stable identifiers; actual user-facing text should be handled by the FE via i18n.

## League Memberships (Join/Leave)

- MAXIMUM_LEAGUES_REACHED

  - Context: User attempted to join/create a league but has reached the limit (5)
  - Extra: none

- LEAGUE_PRIVATE_JOIN_REQUEST_REQUIRED

  - Context: Attempted to join a private league directly; a join request is required
  - Extra: none

- YOUTUBE_NOT_CONNECTED

  - Context: Attempted to join a subscriber-only league, but the user hasn’t connected YouTube
  - Extra: youtube_channel_id

- YOUTUBE_SCOPE_UPGRADE_REQUIRED

  - Context: Attempted to join a subscriber-only league, but the user’s OAuth scope is insufficient (needs upgrade)
  - Extra: youtube_channel_id, needs_scope_upgrade: true

- YOUTUBE_NOT_SUBSCRIBED

  - Context: Attempted to join a subscriber-only league, but the user is not subscribed to the league owner’s channel
  - Extra: youtube_channel_id

- ALREADY_A_MEMBER

  - Context: Attempted to join a league where the user is already a member
  - Extra: none

- LEAGUE_JOIN_SUCCESS

  - Context: Successfully joined a league
  - Extra: league { id, name, youtube_league, subscriber_only }

- LEAGUE_JOIN_FAILED

  - Context: Failed to join a league due to validation errors
  - Extra: errors (array of validation messages)

- LEAVE_LEAGUE_SUCCESS

  - Context: Successfully left a league
  - Extra: none

- LEAVE_LEAGUE_NOT_MEMBER
  - Context: Attempted to leave a league the user is not a member of
  - Extra: none

## YouTube General/Eligibility

- MISSING_CHANNEL_ID

  - Context: Channel id parameter missing for subscription/status endpoints
  - Extra: none

- YOUTUBE_NOT_CONNECTED

  - Context: User’s YouTube account not connected (also used in membership checks)
  - Extra: possibly youtube_channel_id in some contexts

- YOUTUBE_SCOPE_UPGRADE_REQUIRED

  - Context: User lacks elevated YouTube scope for subscription checks (also used in membership checks)
  - Extra: needs_scope_upgrade: true, possibly youtube_channel_id in some contexts

- YOUTUBE_SCOPE_UPGRADE_REQUIRED_FOR_CREATOR

  - Context: User lacks elevated scope to enable creator features
  - Extra: needs_scope_upgrade: true

- CREATOR_MODE_TOO_MANY_ATTEMPTS

  - Context: Rate limit exceeded when toggling creator mode
  - Extra: retry_after (seconds)

- YOUTUBE_CHANNEL_REQUIRED_FOR_CREATOR

  - Context: Attempt to enable content creator mode without having a connected channel
  - Extra: none

- CREATOR_MODE_MIN_SUBSCRIBERS_NOT_MET

  - Context: Attempt to enable creator mode without meeting minimum subscribers
  - Extra: subscriber_count, min_subscribers_required

- CREATOR_MODE_UPDATED
  - Context: Successfully updated content creator status
  - Extra: is_content_creator

## Leagues Owner/Management

- LEAGUE_OWNER_ONLY

  - Context: Attempt to perform an owner-only action (edit/update/archive, etc.) by a non-owner
  - Extra: none

- Open/Private toggle
  - Endpoint: PATCH /api/v1/leagues/:id with league: { open: true|false }
  - Authorization: Owner only (returns LEAGUE_OWNER_ONLY if unauthorized)
  - Response: Currently returns full league JSON on success (no message_key). If FE needs a message_key, propose introducing:
    - LEAGUE_OPEN_STATE_UPDATED
    - Extra: league { id, name, open }

## Notes for Frontend Integration

- Keys are returned in JSON as `error_key` for errors and `message_key` for successes, alongside any relevant data.
- Some legacy endpoints still return `message` or `errors` arrays. As we migrate, prefer handling `error_key`/`message_key` when present.
- For YouTube scope upgrades, the FE should guide the user through the scope upgrade flow when `needs_scope_upgrade: true` is present.
- For subscriber-only league joins, the FE can optionally show the `youtube_channel_id` metadata in CTAs.
