openapi: 3.0.3
info:
  title: BragRights Admin API
  version: 1.0.0
  description: Admin endpoints for League and User management
servers:
  - url: http://api.bragrights.football/api/v1
paths:
  /admin/leagues:
    get:
      summary: List leagues
      parameters:
        - in: query
          name: page
          schema: { type: integer }
        - in: query
          name: per_page
          schema: { type: integer }
        - in: query
          name: q
          schema: { type: string }
        - in: query
          name: archived
          schema: { type: boolean }
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AdminLeague'
                  pagination:
                    $ref: '#/components/schemas/PaginationMeta'
  /admin/leagues/{id}:
    get:
      summary: Show league
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/AdminLeague'
    put:
      summary: Update league
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                league: { $ref: '#/components/schemas/AdminLeagueUpdate' }
      responses:
        '200': { description: Updated }
    delete:
      summary: Archive league
      responses:
        '200': { description: Archived }
  /admin/leagues/{id}/transfer_ownership:
    post:
      summary: Transfer league ownership
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                transfer:
                  type: object
                  properties:
                    new_owner_id: { type: integer }
                  required: [new_owner_id]
      responses:
        '200': { description: Transferred }
  /admin/leagues/bulk_action:
    post:
      summary: Bulk league operations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: array
                  items: { type: integer }
                action_name:
                  type: string
                  enum: [archive, unarchive]
              required: [ids, action_name]
      responses:
        '200': { description: OK }
  /admin/users:
    get:
      summary: List users
      parameters:
        - in: query
          name: email
          schema: { type: string }
        - in: query
          name: username
          schema: { type: string }
        - in: query
          name: status
          schema: { type: string, enum: [active, deleted, all] }
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items: { $ref: '#/components/schemas/AdminUser' }
                  pagination: { $ref: '#/components/schemas/PaginationMeta' }
  /admin/users/{id}:
    get:
      summary: Show user
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
      responses:
        '200': { description: OK }
    patch:
      summary: Update user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email: { type: string }
                    username: { type: string }
                    admin: { type: boolean }
      responses:
        '200': { description: Updated }
    delete:
      summary: Soft delete user
      responses:
        '200': { description: Deleted }
  /admin/users/{id}/restore:
    post:
      summary: Restore user
      responses:
        '200': { description: Restored }
  /admin/users/{id}/toggle_admin:
    post:
      summary: Toggle admin flag
      responses:
        '200': { description: Toggled }
components:
  schemas:
    AdminUser:
      type: object
      properties:
        id: { type: integer }
        email: { type: string }
        username: { type: string }
        admin: { type: boolean }
        is_content_creator: { type: boolean }
        youtube_channel_id: { type: string, nullable: true }
        youtube_channel_name: { type: string, nullable: true }
        youtube_subscriber_count: { type: integer, nullable: true }
    AdminLeague:
      type: object
      properties:
        id: { type: integer }
        name: { type: string }
        open: { type: boolean }
        archived: { type: boolean }
        archived_at: { type: string, format: date-time, nullable: true }
        starting_matchday: { type: integer, nullable: true }
        youtube_league: { type: boolean }
        subscriber_only: { type: boolean }
        youtube_channel_id: { type: string, nullable: true }
        owner: { $ref: '#/components/schemas/AdminUser' }
        competition:
          type: object
          nullable: true
          properties:
            id: { type: integer }
            name: { type: string }
            code: { type: string }
        season:
          type: object
          nullable: true
          properties:
            id: { type: integer }
            start_date: { type: string, format: date }
            end_date: { type: string, format: date }
        member_count: { type: integer }
        created_at: { type: string, format: date-time }
        updated_at: { type: string, format: date-time }
    AdminLeagueUpdate:
      type: object
      properties:
        name: { type: string }
        open: { type: boolean }
        competition_id: { type: integer }
        season_id: { type: integer }
        starting_matchday: { type: integer }
        youtube_league: { type: boolean }
        subscriber_only: { type: boolean }
        youtube_channel_id: { type: string }
        subscriber_requirement_type: { type: string }
        min_subscriber_date: { type: string, format: date-time }
    PaginationMeta:
      type: object
      properties:
        page: { type: integer }
        per_page: { type: integer }
        total_pages: { type: integer }
        total_count: { type: integer }

