openapi: 3.0.3
info:
  title: BragRights Round Scoring API
  version: 1.0.0
  description: |
    Endpoints to retrieve round scores and winners for leagues and users.
    Note: Endpoints are available under both aliases:
    - /api/v1/leagues/{id}/rounds/{round_number}/...
    - /api/v1/youtube/leagues/{id}/rounds/{round_number}/...
servers:
  - url: /api/v1
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    ScoreBreakdownItem:
      type: object
      properties:
        match_id:
          type: integer
        points:
          type: integer
      required: [match_id, points]
    LeagueRoundScore:
      type: object
      properties:
        user_id:
          type: integer
        username:
          type: string
        round_number:
          type: integer
        total_points:
          type: integer
        breakdown:
          type: array
          items:
            $ref: '#/components/schemas/ScoreBreakdownItem'
      required: [user_id, username, round_number, total_points, breakdown]
    LeagueRoundScoresResponse:
      type: object
      properties:
        league_id:
          type: integer
        round_number:
          type: integer
        scores:
          type: array
          items:
            $ref: '#/components/schemas/LeagueRoundScore'
      required: [league_id, round_number, scores]
    LeagueRoundWinner:
      type: object
      properties:
        rank:
          type: integer
        user_id:
          type: integer
        username:
          type: string
        total_points:
          type: integer
      required: [rank, user_id, username, total_points]
    LeagueRoundWinnersResponse:
      type: object
      properties:
        league_id:
          type: integer
        round_number:
          type: integer
        winners:
          type: array
          items:
            $ref: '#/components/schemas/LeagueRoundWinner'
      required: [league_id, round_number, winners]
    UserRoundScoreResponse:
      type: object
      properties:
        user_id:
          type: integer
        username:
          type: string
        round_number:
          type: integer
        total_points:
          type: integer
        breakdown:
          type: array
          items:
            $ref: '#/components/schemas/ScoreBreakdownItem'
      required: [user_id, username, round_number, total_points, breakdown]
    ApiError:
      type: object
      properties:
        error:
          type: string
        error_key:
          type: string
        message:
          type: string
paths:
  /leagues/{id}/rounds/{round_number}/scores:
    get:
      summary: Get league round scores
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
          description: League ID
        - in: path
          name: round_number
          schema:
            type: integer
          required: true
          description: Matchday/round number
      responses:
        '200':
          description: Round scores for all members, sorted by total_points desc
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeagueRoundScoresResponse'
        '403':
          description: Round scores are hidden by league owner
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
              examples:
                hidden:
                  value:
                    error_key: LEAGUE_ROUND_SCORES_HIDDEN
        '401':
          description: Unauthorized
  /leagues/{id}/rounds/{round_number}/winners:
    get:
      summary: Get top 3 winners for a league round
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
        - in: path
          name: round_number
          schema:
            type: integer
          required: true
      responses:
        '200':
          description: Top 3 winners by round points
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeagueRoundWinnersResponse'
        '403':
          description: Round scores are hidden by league owner
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
              examples:
                hidden:
                  value:
                    error_key: LEAGUE_ROUND_SCORES_HIDDEN
        '401':
          description: Unauthorized
  /users/{id}/rounds/{round_number}/score:
    get:
      summary: Get a specific user's round score
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
          description: User ID
        - in: path
          name: round_number
          schema:
            type: integer
          required: true
        - in: query
          name: season_id
          schema:
            type: integer
          required: false
          description: Season ID (optional; if omitted, controller must infer)
      responses:
        '200':
          description: User round score
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoundScoreResponse'
        '404':
          description: No round prediction found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          description: Unauthorized

