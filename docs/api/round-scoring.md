# Round Scoring API

## GET /api/v1/leagues/:id/rounds/:round_number/scores
Auth: Bearer token

200 OK
```json
{
  "league_id": 42,
  "round_number": 5,
  "scores": [
    {
      "user_id": 7,
      "username": "john",
      "round_number": 5,
      "total_points": 6,
      "breakdown": [ {"match_id": 1001, "points": 3}, {"match_id": 1002, "points": 3} ]
    }
  ]
}
```

## GET /api/v1/leagues/:id/rounds/:round_number/winners

200 OK
```json
{
  "league_id": 42,
  "round_number": 5,
  "winners": [
    { "rank": 1, "user_id": 7, "username": "john", "total_points": 9 },
    { "rank": 2, "user_id": 12, "username": "mary", "total_points": 7 },
    { "rank": 3, "user_id": 2, "username": "bill", "total_points": 6 }
  ]
}
```

## GET /api/v1/users/:id/rounds/:round_number/score

200 OK
```json
{
  "user_id": 7,
  "username": "john",
  "round_number": 5,
  "total_points": 6,
  "breakdown": [ {"match_id": 1001, "points": 3}, {"match_id": 1002, "points": 3} ]
}
```

404 if no prediction for user/round.

## TypeScript Contracts

```ts
export interface RoundScoreBreakdown { match_id: number; points: 0|1|3|null }

export interface LeagueRoundScoresResponse {
  league_id: number;
  round_number: number;
  scores: { user_id: number; username: string; round_number: number; total_points: number; breakdown: RoundScoreBreakdown[] }[];
}

export interface LeagueRoundWinnersResponse {
  league_id: number;
  round_number: number;
  winners: { rank: number; user_id: number; username: string; total_points: number }[];
}

export interface UserRoundScoreResponse {
  user_id: number;
  username: string;
  round_number: number;
  total_points: number;
  breakdown: RoundScoreBreakdown[];
}
```

## curl Examples

```bash
curl -H "Authorization: Bearer $TOKEN" "$API_URL/api/v1/leagues/42/rounds/5/scores"

curl -H "Authorization: Bearer $TOKEN" "$API_URL/api/v1/leagues/42/rounds/5/winners"

curl -H "Authorization: Bearer $TOKEN" "$API_URL/api/v1/users/7/rounds/5/score"
```

