openapi: 3.0.3
info:
  title: BragRights League Customization API
  version: 1.0.0
  description: API for customizing league presentation and branding
servers:
  - url: http://api.bragrights.football/api/v1
paths:
  /leagues/{league_id}/customization:
    get:
      summary: Get league customization
      description: Returns the customization settings for a league. Requires authentication. Anyone can read; only owners who are content creators can update.
      parameters:
        - name: league_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeagueCustomizationResponse"
        "401":
          description: Unauthorized
        "404":
          description: League not found
    put:
      summary: Update league customization
      description: Updates customization for a league. Only the league owner who is a content creator can update.
      parameters:
        - name: league_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LeagueCustomizationRequest"
      responses:
        "200":
          description: Updated customization
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeagueCustomizationResponse"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden (not owner or not a content creator)
        "422":
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
components:
  schemas:
    ColorScheme:
      type: object
      properties:
        primaryBg:
          type: string
          description: Hex color string (e.g., #112233)
        secondaryBg:
          type: string
        primaryText:
          type: string
        secondaryText:
          type: string
        accent:
          type: string
        tabBg:
          type: string
        tabActive:
          type: string
        headerBg:
          type: string
    LeagueCustomizationRequest:
      type: object
      required:
        - customization
      properties:
        customization:
          type: object
          properties:
            customHeader:
              type: string
              maxLength: 120
            headerFont:
              type: string
            headerPlacement:
              type: string
              enum: [above_league_name, below_league_name, above_tabs]
            logoUrl:
              type: string
              format: uri
            logoPosition:
              type: string
              enum: [left, center, right]
            logoSize:
              type: string
              enum: [small, medium, large]
            colorScheme:
              $ref: "#/components/schemas/ColorScheme"
    LeagueCustomizationResponse:
      type: object
      properties:
        leagueId:
          type: integer
        canCustomizeLeague:
          type: boolean
          description: Whether the current user can customize this league (owner AND content creator)
        customHeader:
          type: string
        headerFont:
          type: string
        headerPlacement:
          type: string
        logoUrl:
          type: string
        logoPosition:
          type: string
        logoSize:
          type: string
        colorScheme:
          $ref: "#/components/schemas/ColorScheme"
