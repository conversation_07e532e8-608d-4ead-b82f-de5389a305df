# Team Standings API Guide

This API allows users to view standings for fans of a specific team. It shows how users who have selected a particular team as their favorite are performing in predictions.

## Endpoint

### Get Team Standings

Retrieve standings for all users who have selected a specific team as their favorite.

```
GET /api/v1/teams/:id/standings
```

#### Parameters

| Name           | Type    | Description                                                |
| -------------- | ------- | ---------------------------------------------------------- |
| id             | integer | The ID of the team (required)                              |
| competition_id | integer | Optional - Filter standings by a specific competition      |
| season_id      | integer | Optional - Filter standings by a specific season           |

#### Response

```json
{
  "team": {
    "id": 123,
    "name": "Manchester United",
    "short_name": "Man United",
    "tla": "M<PERSON>",
    "crest_public_id": "teams/crests/man_utd_logo",
    "club_colors": {
      "primary": "#DA291C",
      "secondary": "#FBE122"
    }
  },
  "competition": {
    "id": 39,
    "name": "Premier League",
    "emblem_public_id": "competitions/emblems/premier_league"
  },
  "season": {
    "id": 1234,
    "start_date": "2023-08-11",
    "end_date": "2024-05-19"
  },
  "standings": [
    {
      "username": "superfan123",
      "points": 120,
      "correct": 45,
      "incorrect": 15,
      "perfect": 10,
      "position": 1,
      "top_message": "🏆 superfan123 is leading among fans! Congratulations!"
    },
    {
      "username": "reddevil22",
      "points": 105,
      "correct": 40,
      "incorrect": 20,
      "perfect": 8,
      "position": 2,
      "top_message": "🥈 reddevil22 is in second place. Keep pushing!"
    },
    {
      "username": "unitedforever",
      "points": 90,
      "correct": 35,
      "incorrect": 25,
      "perfect": 6,
      "position": 3,
      "top_message": "🥉 unitedforever is in third place. Great job!"
    }
  ],
  "message": ["Team fans standings calculated"],
  "status": 200,
  "type": "success"
}
```

If the team has no fans:

```json
{
  "team": {
    "id": 123,
    "name": "Manchester United",
    "short_name": "Man United",
    "tla": "MUN",
    "crest_public_id": "teams/crests/man_utd_logo",
    "club_colors": {
      "primary": "#DA291C",
      "secondary": "#FBE122"
    }
  },
  "standings": [],
  "message": ["No fans found for this team"],
  "status": 200,
  "type": "success"
}
```

If the team does not exist:

```json
{
  "errors": ["Team not found"]
}
```

## Integration Example

Here's an example of how to integrate with the Team Standings API using TypeScript and axios:

```typescript
interface TeamStanding {
  username: string;
  points: number;
  correct: number;
  incorrect: number;
  perfect: number;
  position: number;
  top_message?: string;
}

interface TeamStandingsResponse {
  team: {
    id: number;
    name: string;
    short_name: string;
    tla: string;
    crest_public_id: string;
    club_colors: {
      primary: string;
      secondary: string;
      tertiary?: string;
    };
  };
  competition?: {
    id: number;
    name: string;
    emblem_public_id: string;
  };
  season?: {
    id: number;
    start_date: string;
    end_date: string;
  };
  standings: TeamStanding[];
  message: string[];
  status: number;
  type: string;
}

// Get standings for a team
const getTeamStandings = async (
  teamId: number,
  options?: { competitionId?: number; seasonId?: number }
): Promise<TeamStandingsResponse> => {
  let url = `/api/v1/teams/${teamId}/standings`;
  
  // Add query parameters if provided
  const params = new URLSearchParams();
  if (options?.competitionId) {
    params.append('competition_id', options.competitionId.toString());
  }
  if (options?.seasonId) {
    params.append('season_id', options.seasonId.toString());
  }
  
  // Append query parameters to URL if any exist
  const queryString = params.toString();
  if (queryString) {
    url += `?${queryString}`;
  }
  
  const response = await axios.get(url);
  return response.data;
};
```

## Error Handling

The API uses standard HTTP status codes:

- 200: Success
- 401: Unauthorized (not authenticated)
- 404: Not Found (team doesn't exist)

## Authentication

This endpoint requires authentication. Include the authentication token in the request headers:

```typescript
axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
```

The API will return a 401 Unauthorized status code if the request is not properly authenticated.

## Use Cases

1. **Team Fan Leaderboards**: Display a leaderboard of users who support the same team, showing how they're performing in predictions.
2. **Competition-Specific Performance**: Filter standings by competition to see how fans of a team are performing in a specific competition.
3. **Season Comparison**: Filter standings by season to compare fan performance across different seasons.
4. **Fan Community Building**: Encourage friendly competition among fans of the same team.
