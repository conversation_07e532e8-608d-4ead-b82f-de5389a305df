# Round Predictions API Guide

## Overview

The Round Predictions API allows users to create and manage predictions for matches across different competitions, seasons, and matchdays. This API now supports both individual and batch operations for more efficient handling of multiple predictions.

## Key Features

- Create predictions for individual rounds
- Update existing predictions
- <PERSON><PERSON> create multiple round predictions in a single request
- <PERSON><PERSON> update multiple round predictions in a single request
- Track partial successes with detailed error messages

## API Endpoints

### Get User's Round Predictions

**Endpoint:** `GET /api/v1/round_predictions`

**Parameters:**
- `season_id` (optional) - Filter by season
- `competition_id` (optional) - Filter by competition
- `matchday` (optional) - Filter by a single matchday
- `matchdays` (optional) - Filter by multiple matchdays (comma-separated list or array)

**Example Requests:**

Single matchday:
```http
GET /api/v1/round_predictions?competition_id=2&matchday=15
```

Multiple matchdays:
```http
GET /api/v1/round_predictions?competition_id=2&matchdays=15,16,17
```

**Response:**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        {
          "id": 456,
          "match_id": 789,
          "home_score": 2,
          "away_score": 1,
          "points": null,
          "match": {
            "id": 789,
            "home_team": { "name": "Chelsea FC" },
            "away_team": { "name": "Everton FC" }
          }
        }
        // More match predictions...
      ]
    },
    {
      "id": 124,
      "matchday": 16,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Match predictions...
      ]
    }
    // More round predictions...
  ],
  "message": ["Round predictions found"],
  "status": 200,
  "type": "success"
}
```

### Get Single Round Prediction

**Endpoint:** `GET /api/v1/round_predictions/:id`

**Example Request:**
```http
GET /api/v1/round_predictions/123
```

**Response:**
```json
{
  "round_predictions": {
    "id": 123,
    "matchday": 15,
    "season_id": 1,
    "competition_id": 2,
    "stage": "REGULAR_SEASON",
    "match_predictions": [
      // Match predictions...
    ]
  },
  "message": ["Round prediction found"],
  "status": 200,
  "type": "success"
}
```

### Create Round Prediction

**Endpoint:** `POST /api/v1/round_predictions`

**Behavior:**
The endpoint now checks if a round prediction already exists for the provided combination of user, matchday, season, and competition. If one exists, it will update the existing record instead of creating a new one.

**Request Body:**
```json
{
  "round_predictions": {
    "matchday": 15,
    "season_id": 1,
    "competition_id": 2,
    "stage": "REGULAR_SEASON",
    "match_predictions": [
      {
        "match_id": 789,
        "home_score": 2,
        "away_score": 1
      },
      {
        "match_id": 790,
        "home_score": 0,
        "away_score": 3
      }
    ]
  }
}
```

**Response (New Prediction Created):**
```json
{
  "round_predictions": {
    "id": 123,
    "matchday": 15,
    "season_id": 1,
    "competition_id": 2,
    "stage": "REGULAR_SEASON",
    "match_predictions": [
      // Created match predictions...
    ]
  },
  "message": ["Round prediction created"],
  "status": 200,
  "type": "success"
}
```

**Response (Existing Prediction Updated):**
```json
{
  "round_predictions": {
    "id": 123,
    "matchday": 15,
    "season_id": 1,
    "competition_id": 2,
    "stage": "REGULAR_SEASON",
    "match_predictions": [
      // Updated match predictions...
    ]
  },
  "message": ["Existing round prediction updated"],
  "status": 200,
  "type": "success"
}
```

### Update Round Prediction

**Endpoint:** `PUT /api/v1/round_predictions/:id`

**Request Body:**
```json
{
  "round_predictions": {
    "match_predictions": [
      {
        "match_id": 789,
        "home_score": 3,
        "away_score": 1
      }
    ]
  }
}
```

**Response:**
```json
{
  "round_predictions": {
    "id": 123,
    "matchday": 15,
    "season_id": 1,
    "competition_id": 2,
    "stage": "REGULAR_SEASON",
    "match_predictions": [
      // Updated match predictions...
    ]
  },
  "message": ["Round prediction updated"],
  "status": 200,
  "type": "success"
}
```

### Batch Create Round Predictions

**Endpoint:** `POST /api/v1/round_predictions/batch_create`

**Request Body:**
```json
{
  "round_predictions": [
    {
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        {
          "match_id": 789,
          "home_score": 2,
          "away_score": 1
        },
        {
          "match_id": 790,
          "home_score": 0,
          "away_score": 3
        }
      ]
    },
    {
      "matchday": 16,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        {
          "match_id": 801,
          "home_score": 1,
          "away_score": 1
        }
      ]
    }
  ]
}
```

**Response (All Successful):**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Match predictions for matchday 15...
      ]
    },
    {
      "id": 124,
      "matchday": 16,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Match predictions for matchday 16...
      ]
    }
  ],
  "message": ["Successfully created 2 round predictions"],
  "status": 200,
  "type": "success"
}
```

**Response (Partial Success):**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Match predictions for matchday 15...
      ]
    }
  ],
  "failed": [
    {
      "data": {
        "matchday": 16,
        "season_id": 1,
        "competition_id": 2,
        "stage": "REGULAR_SEASON",
        "match_predictions": [
          {
            "match_id": 801,
            "home_score": 1,
            "away_score": 1
          }
        ]
      },
      "errors": [
        "Round prediction for this matchday already exists"
      ]
    }
  ],
  "message": ["Created 1 predictions with 1 failures"],
  "status": 207,
  "type": "partial_success"
}
```

### Batch Update Round Predictions

**Endpoint:** `PUT /api/v1/round_predictions/batch_update`

**Request Body:**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "match_predictions": [
        {
          "match_id": 789,
          "home_score": 3,
          "away_score": 2
        }
      ]
    },
    {
      "id": 124,
      "match_predictions": [
        {
          "match_id": 801,
          "home_score": 2,
          "away_score": 2
        }
      ]
    }
  ]
}
```

**Response (All Successful):**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Updated match predictions for matchday 15...
      ]
    },
    {
      "id": 124,
      "matchday": 16,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Updated match predictions for matchday 16...
      ]
    }
  ],
  "message": ["Successfully updated 2 round predictions"],
  "status": 200,
  "type": "success"
}
```

**Response (Partial Success):**
```json
{
  "round_predictions": [
    {
      "id": 123,
      "matchday": 15,
      "season_id": 1,
      "competition_id": 2,
      "stage": "REGULAR_SEASON",
      "match_predictions": [
        // Updated match predictions for matchday 15...
      ]
    }
  ],
  "failed": [
    {
      "data": {
        "id": 124,
        "match_predictions": [
          {
            "match_id": 801,
            "home_score": 2,
            "away_score": 2
          }
        ]
      },
      "error": "Round prediction not found"
    }
  ],
  "message": ["Updated 1 predictions with 1 failures"],
  "status": 207,
  "type": "partial_success"
}
```

## Data Models

### Round Prediction Object

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier for the round prediction |
| matchday | Integer | The matchday number |
| season_id | Integer | ID of the season |
| competition_id | Integer | ID of the competition |
| stage | String | Competition stage (e.g., "REGULAR_SEASON", "LEAGUE_STAGE") |
| match_predictions | Array | Array of match prediction objects |

### Match Prediction Object

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier for the match prediction |
| match_id | Integer | ID of the match being predicted |
| home_score | Integer | Predicted home team score |
| away_score | Integer | Predicted away team score |
| points | Integer or null | Points awarded for the prediction (null if match not finished) |
| match | Object | Match details (included in responses) |

## Implementation Notes

### Batch Processing

The batch endpoints offer significant performance improvements when submitting predictions for multiple rounds:

1. **Network Efficiency**: Reduces the number of HTTP requests needed
2. **Transaction Handling**: Backend processes multiple predictions efficiently
3. **Error Handling**: Provides detailed feedback on which predictions succeeded or failed

### Validation Rules

- Round predictions must have a unique combination of user_id, season_id, and matchday
- Match scores must be non-negative integers
- Users can only create/update their own predictions
- Points cannot be changed once awarded

### Status Codes

- 200: Success
- 207: Multi-Status (partial success)
- 403: Unauthorized (when trying to update another user's predictions)
- 404: Not found
- 422: Validation errors
- 500: Server error

## Frontend Integration Examples

### Creating Multiple Round Predictions

```typescript
const createMultipleRoundPredictions = async (predictions) => {
  try {
    const response = await api.post('/api/v1/round_predictions/batch_create', {
      round_predictions: predictions
    });
    
    // Handle partial success
    if (response.data.failed && response.data.failed.length > 0) {
      console.warn('Some predictions failed:', response.data.failed);
    }
    
    return response.data.round_predictions;
  } catch (error) {
    console.error('Error creating round predictions:', error);
    throw error;
  }
};

// Example usage
const predictions = [
  {
    matchday: 15,
    season_id: 1,
    competition_id: 2,
    stage: 'REGULAR_SEASON',
    match_predictions: [
      { match_id: 101, home_score: 2, away_score: 1 },
      { match_id: 102, home_score: 0, away_score: 0 }
    ]
  },
  {
    matchday: 16,
    season_id: 1,
    competition_id: 2,
    stage: 'REGULAR_SEASON',
    match_predictions: [
      { match_id: 201, home_score: 3, away_score: 2 }
    ]
  }
];

createMultipleRoundPredictions(predictions)
  .then(savedPredictions => {
    console.log(`Successfully saved ${savedPredictions.length} round predictions`);
  });
```

### Updating Multiple Round Predictions

```typescript
const updateMultipleRoundPredictions = async (predictions) => {
  try {
    const response = await api.put('/api/v1/round_predictions/batch_update', {
      round_predictions: predictions
    });
    
    // Handle partial success
    if (response.data.failed && response.data.failed.length > 0) {
      console.warn('Some updates failed:', response.data.failed);
    }
    
    return response.data.round_predictions;
  } catch (error) {
    console.error('Error updating round predictions:', error);
    throw error;
  }
};

// Example usage
const updates = [
  {
    id: 123,
    match_predictions: [
      { match_id: 101, home_score: 3, away_score: 1 }
    ]
  },
  {
    id: 124,
    match_predictions: [
      { match_id: 201, home_score: 2, away_score: 3 }
    ]
  }
];

updateMultipleRoundPredictions(updates);
```

### Fetching Multiple Round Predictions

```typescript
const fetchMultipleRoundPredictions = async (competitionId, seasonId, matchdays) => {
  try {
    const response = await api.get('/api/v1/round_predictions', {
      params: {
        competition_id: competitionId,
        season_id: seasonId,
        matchdays: matchdays.join(',')  // e.g., "15,16,17"
      }
    });
    
    return response.data.round_predictions;
  } catch (error) {
    console.error('Error fetching round predictions:', error);
    throw error;
  }
};

// Example usage
fetchMultipleRoundPredictions(2, 1, [15, 16, 17])
  .then(predictions => {
    console.log(`Fetched ${predictions.length} round predictions`);
  });
```

### Creating or Updating Round Predictions

```typescript
const createOrUpdateRoundPrediction = async (prediction) => {
  try {
    const response = await api.post('/api/v1/round_predictions', {
      round_predictions: prediction
    });
    
    const isNew = response.data.message[0] === 'Round prediction created';
    console.log(isNew ? 'Created new prediction' : 'Updated existing prediction');
    
    return response.data.round_predictions;
  } catch (error) {
    console.error('Error with round prediction:', error);
    throw error;
  }
};
```

## Error Handling

```typescript
const handleRoundPredictionErrors = (error) => {
  if (error.response) {
    if (error.response.status === 207) {
      // Partial success - handle the failures
      const failures = error.response.data.failed || [];
      const messages = failures.map(f => f.errors?.join(', ') || f.error || 'Unknown error');
      return `Some predictions couldn't be saved: ${messages.join('; ')}`;
    } else if (error.response.status === 422) {
      // Validation errors
      return 'Please correct the errors in your predictions';
    } else if (error.response.status === 403) {
      return 'You are not authorized to update these predictions';
    }
  }
  
  return 'An unexpected error occurred while saving predictions';
};
```
