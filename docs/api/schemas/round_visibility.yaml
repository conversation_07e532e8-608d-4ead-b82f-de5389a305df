openapi: 3.0.3
info:
  title: Round Visibility & Showcase API
  version: "1.0.0"
paths:
  /api/v1/leagues/{id}/round_visibility:
    get:
      summary: List round visibility records
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/RoundVisibility"
  /api/v1/leagues/{id}/round_visibility_defaults:
    patch:
      summary: Update default visibility setting
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [default_visible_for_new_rounds]
              properties:
                default_visible_for_new_rounds:
                  type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      league_id: { type: integer }
                      default_visible_for_new_rounds: { type: boolean }
        "400": { description: NO_VISIBILITY_PARAMS }
        "401": { description: LEAGUE_OWNER_ONLY }
        "403": { description: NOT_CONTENT_CREATOR }
  /api/v1/leagues/{id}/rounds/{round_number}/publish:
    post:
      summary: Publish a round
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoundVisibilityResponse"
        "400": { description: NO_SEASON_FOR_LEAGUE }
        "401": { description: LEAGUE_OWNER_ONLY }
        "403": { description: NOT_CONTENT_CREATOR }
  /api/v1/leagues/{id}/rounds/publish_bulk:
    post:
      summary: Bulk publish rounds
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [round_numbers]
              properties:
                season_id: { type: integer }
                round_numbers:
                  type: array
                  items: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/RoundVisibility"
        "400": { description: INVALID_PARAMS }
        "401": { description: LEAGUE_OWNER_ONLY }
        "403": { description: NOT_CONTENT_CREATOR }
  /api/v1/leagues/{id}/rounds/{round_number}/showcase_users:
    post:
      summary: Add showcased users to a round
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [user_ids]
              properties:
                season_id: { type: integer }
                user_ids:
                  type: array
                  items: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: "#/components/schemas/ShowcaseState"
        "401": { description: LEAGUE_OWNER_ONLY }
        "403": { description: NOT_CONTENT_CREATOR }
        "422": { description: SHOWCASE_LIMIT_EXCEEDED }
  /api/v1/leagues/{id}/rounds/{round_number}/showcase_users/{user_id}:
    delete:
      summary: Remove a showcased user from a round
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
        - in: path
          name: user_id
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: "#/components/schemas/ShowcaseState"
        "404": { description: NOT_FOUND }
        "401": { description: LEAGUE_OWNER_ONLY }
        "403": { description: NOT_CONTENT_CREATOR }
  /api/v1/leagues/{id}/rounds/{round_number}/users/{user_id}/score:
    get:
      summary: Get a user's round score in a league scope
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
        - in: path
          name: user_id
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserRoundScore"
        "403": { description: USER_ROUND_SCORE_HIDDEN }
        "404": { description: Not found }
/api/v1/leagues/{id}/rounds/{round_number}/scores:
  get:
    summary: Get round scores for a league (may be partial when unpublished)
    description: |
        Returns round scores for members of a league. If the round is unpublished and the league is in
        creator-controlled mode with a positive `showcase_user_limit`, the response contains only the top-N
        users according to that limit. Otherwise, full results are returned when visible.
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoundScoresResponse"
        "403": { description: LEAGUE_ROUND_SCORES_HIDDEN }
  /api/v1/leagues/{id}/rounds/{round_number}/winners:
    get:
      summary: Get top winners for a round (may be capped when unpublished)
      description: |
        Returns top winners for a round. If the round is unpublished and automatic top-N showcase is active,
        the winners are capped to min(N, 3). Otherwise, top 3 winners are returned.
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: integer }
        - in: path
          name: round_number
          required: true
          schema: { type: integer }
        - in: query
          name: season_id
          required: false
          schema: { type: integer }
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoundWinnersResponse"
        "403": { description: LEAGUE_ROUND_SCORES_HIDDEN }
components:
  schemas:
    RoundVisibility:
      type: object
      properties:
        league_id: { type: integer }
        season_id: { type: integer }
        round_number: { type: integer }
        visible: { type: boolean }
        visible_at: { type: string, format: date-time, nullable: true }
    RoundVisibilityResponse:
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RoundVisibility"
    ShowcaseState:
      type: object
      properties:
        league_id: { type: integer }
        season_id: { type: integer }
        round_number: { type: integer }
        showcased_user_ids:
          type: array
          items: { type: integer }
    UserRoundScore:
      type: object
      properties:
        user_id: { type: integer }
        username: { type: string }
        round_number: { type: integer }
        total_points: { type: integer }
        breakdown:
          type: array
          items:
            type: object
            properties:
              match_id: { type: integer }
              points: { type: integer }

    RoundScoresResponse:
      type: object
      properties:
        league_id: { type: integer }
        round_number: { type: integer }
        scores:
          type: array
          items:
            type: object
            properties:
              user_id: { type: integer }
              username: { type: string }
              total_points: { type: integer }
              breakdown:
                type: array
                items:
                  type: object
                  properties:
                    match_id: { type: integer }
                    points: { type: integer }
    RoundWinnersResponse:
      type: object
      properties:
        league_id: { type: integer }
        round_number: { type: integer }
        winners:
          type: array
          items:
            type: object
            properties:
              rank: { type: integer }
              user_id: { type: integer }
              username: { type: string }
              total_points: { type: integer }
