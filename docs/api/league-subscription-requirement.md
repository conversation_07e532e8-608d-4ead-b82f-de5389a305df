# League Subscription Requirement API

PATCH /api/v1/leagues/:id/subscription_requirement

- Auth: Bearer token required
- Authorization: League owner only

## Request

Query/Body Parameters:
- require_subscription: boolean (required)

Example:
```json
{
  "require_subscription": true
}
```

Validation rules when enabling (true):
- League must have youtube_channel_id
- Owner must be eligible per YoutubeEligibilityService

## Responses

200 OK:
```json
{
  "id": 42,
  "name": "Creator League",
  "subscriber_only": true,
  "youtube_channel_id": "UC123...",
  "owner_id": 7
}
```

400 Bad Request (missing param):
```json
{ "errors": ["Missing parameter: require_subscription"] }
```

401 Unauthorized (no token):
```json
{ "error": "You need to sign in or sign up before continuing." }
```

403 Forbidden (not owner):
```json
{ "errors": ["Only the league owner can perform this action"] }
```

422 Unprocessable Entity (validation):
```json
{ "errors": ["YouTube channel ID is required to enable subscriber-only leagues"] }
```

or

```json
{
  "errors": ["Cannot enable subscriber-only: You need at least 100 subscribers to create a subscriber-only league"],
  "min_subscribers_required": 100
}
```

## TypeScript Contracts

```ts
export interface PatchLeagueSubscriptionRequirementRequest {
  require_subscription: boolean;
}

export interface LeagueResponse {
  id: number;
  name: string;
  subscriber_only: boolean;
  youtube_channel_id: string | null;
  owner_id: number;
}

export type PatchLeagueSubscriptionRequirementResponse = LeagueResponse;
```

## curl Example

```bash
curl -X PATCH \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"require_subscription":true}' \
  "$API_URL/api/v1/leagues/42/subscription_requirement"
```

