openapi: 3.0.3
info:
  title: YouTube Quota Exhaustion Responses
  version: 1.0.0
  description: Documents 429 quota exhaustion responses for relevant endpoints
servers:
  - url: http://api.bragrights.football
paths:
  /api/v1/youtube/subscription_status:
    get:
      summary: Get subscription status to a channel
      tags: [YouTube]
      security: [{ BearerAuth: [] }]
      parameters:
        - in: query
          name: channel_id
          required: true
          schema: { type: string }
      responses:
        '200':
          description: Subscription status
          content:
            application/json:
              schema:
                type: object
                properties:
                  subscribed:
                    type: boolean
                  quota_status:
                    type: object
                    nullable: true
        '401': { description: Unauthorized }
        '403': { description: Scope upgrade required }
        '429':
          description: YouTube quota exhausted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error429'
  /api/v1/leagues/{league_id}/memberships:
    post:
      summary: Join a league (subscriber-only flow may hit quota constraints)
      tags: [Leagues, YouTube]
      security: [{ BearerAuth: [] }]
      parameters:
        - in: path
          name: league_id
          required: true
          schema: { type: string }
      responses:
        '201': { description: Membership created }
        '422':
          description: Validation error or not subscribed
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: YOUTUBE_NOT_SUBSCRIBED
        '429':
          description: YouTube quota exhausted (no cached subscription available)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error429'
components:
  schemas:
    Error429:
      type: object
      required: [error]
      properties:
        error:
          type: string
          enum: [YOUTUBE_QUOTA_EXHAUSTED]
        message:
          type: string
          example: YouTube API quota is temporarily exhausted. Please try again later.
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

