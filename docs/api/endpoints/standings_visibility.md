# Standings Visibility Behavior

The league standings endpoint continues at:
GET /api/v1/leagues/:id/standings

## Behavior
- Owners and admins always see full standings.
- For leagues with scoring_visibility_mode = creator_controlled:
  - Non-owner/non-admin viewers only see standings computed from rounds that are currently visible for the league's season.
  - Visibility is determined by explicit round publications and the default visibility setting when no explicit record exists.
- For leagues with scoring_visibility_mode = always_visible:
  - Behavior remains unchanged; standings are computed from all available data.

## Response Notes
- No schema changes in response, but values (points, ranks) may differ for non-owners in creator_controlled mode.
- Frontend should display a tooltip or info message when standings are visibility-filtered.

## Frontend Guidance
- If the current user is not the league owner/admin and scoring_visibility_mode is creator_controlled, consider showing a banner: "Standings reflect published rounds only."
- Use the Round Visibility API to display which rounds are published, so users understand the partial totals.

