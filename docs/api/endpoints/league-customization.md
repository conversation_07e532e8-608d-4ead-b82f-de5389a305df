# League Customization API

- Endpoint: GET/PUT /api/v1/leagues/{league_id}/customization
- Purpose: Allow league owners who are verified content creators to customize league branding and presentation.
- Auth: Required. Update restricted to owner + content creator.

## Authorization

- Read: Any authenticated user can fetch customization settings.
- Update: Only allowed when `current_devise_api_user.id == league.owner_id` AND `current_user.is_content_creator? == true`.

## Request/Response

See OpenAPI spec: `docs/api/leagues_customization_openapi.yaml`.

### Example Request (PUT)

```json
{
  "customization": {
    "customHeader": "Welcome to my league! 3c0",
    "headerPlacement": "above_league_name",
    "logoUrl": "https://cdn.example.com/logo.png",
    "logoPosition": "left",
    "logoSize": "medium",
    "colorScheme": {
      "primaryBg": "#0a0a0a",
      "primaryText": "#ffffff",
      "accent": "#FFBF00"
    }
  }
}
```

### Example Response (200)

```json
{
  "leagueId": 42,
  "customHeader": "Welcome to my league! 3c0",
  "headerFont": null,
  "headerPlacement": "above_league_name",
  "logoUrl": "https://cdn.example.com/logo.png",
  "logoPosition": "left",
  "logoSize": "medium",
  "colorScheme": {
    "primaryBg": "#0a0a0a",
    "secondaryBg": null,
    "primaryText": "#ffffff",
    "secondaryText": null,
    "accent": "#FFBF00",
    "tabBg": null,
    "tabActive": null,
    "headerBg": null
  }
}
```

## Validation Rules

- customHeader: max 120 chars
- headerPlacement: one of `above_league_name`, `below_league_name`, `above_tabs`
- logoPosition: one of `left`, `center`, `right`
- logoSize: one of `small`, `medium`, `large`
- All colors: valid hex `#RGB` or `#RRGGBB`
- logoUrl: valid http/https URL

## Contracts

- TypeScript contracts: `docs/contracts/LeagueCustomization.ts`
- Frontend examples: `docs/examples/frontend/league-customization.ts`

## Notes

- A default customization record is created automatically when a league is created.
- This feature aligns with content creator branding needs and follows progressive OAuth/permissions approach.
