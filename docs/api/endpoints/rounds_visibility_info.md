# Rounds Visibility Info API

Summarizes which rounds are published and what showcase data is available, per league.

GET /api/v1/leagues/:id/rounds/visibility_info

Query: season_id? (defaults to league.season_id)

Response 200:
```
{
  "data": {
    "league_id": number,
    "season_id": number,
    "published_round_numbers": number[],
    "unpublished_round_numbers": number[],
    "rounds_with_showcase_data": number[],
    "per_round": Array<{
      "round_number": number,
      "visible": boolean,              // published
      "finished": boolean,
      "top_performers_available": boolean,    // Top-N based on showcase_user_limit
      "league_showcased_users_available": boolean, // owner selected users with scores
      "view_permission": "all" | "self" | "owner" | "owner_showcase"
    }>
  }
}
```

Rules encoded
- Published rounds are always visible to all members
- Unpublished rounds: regular members see only their own scores
- Creator-controlled + showcase_user_limit > 0: top-N available for owner to display per round
- League-level showcased users are available to owner across all finished rounds

