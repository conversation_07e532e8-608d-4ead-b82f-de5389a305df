# Round Visibility & Showcase API

This document describes endpoints that let content creators control when and how round scores are displayed, including selective showcases for specific users.

- Audience: Backend and frontend developers
- Auth: All endpoints require authenticated users; owner + content_creator role required for management endpoints

## Overview

- Visibility is controlled per league and season via LeagueRoundVisibility records.
- Default behavior is governed by LeagueRoundVisibilitySetting.default_visible_for_new_rounds.
- Publishing a round sets `visible: true`, makes scores visible to all league members, and records `made_visible_by_user` and `visible_at`.
- Creator-controlled standings: Standings endpoint respects visible rounds for non-owners/admins.
- Showcase now has two distinct features:
  - Round-level Top Performers (Feature A): if `league.showcase_user_limit` is set and the round is unpublished, the top N performers are available for creator display per round.
  - League-level User Showcasing (Feature B): owners select specific users once at league-level; those users can be displayed across all finished rounds regardless of round publish state (does NOT count against Top-N limit).

## Workflow: Publish vs. Showcase

- Showcase users (tease content):
  - Create or find an LRV for the round with `visible: false` (default).
- Automatic top performers:

  - If `scoring_visibility_mode = creator_controlled`, the round is unpublished, default visibility is false, and `showcase_user_limit > 0`, then members see the top N users on:

    - GET /leagues/:id/rounds/:round_number/scores (limited to N users)
    - GET /leagues/:id/rounds/:round_number/winners (limited to min(N, 3))
    - GET /leagues/:id/rounds/:round_number/users/:user_id/score (allowed if user is in top N)

  - Add showcased users to that LRV. Only those users’ round scores can be revealed (via the league-scoped user score endpoint) while the round remains unpublished.
  - `made_visible_by_user` and `visible_at` are NOT required when `visible` is false.

- Publish round (full reveal):
  - Set `visible: true`, `made_visible_by_user: current_owner`, `visible_at: Time.current`.
  - After publishing, all members can see round scores and standings include this round for non-owners/admins.
  - Once published, the round cannot be unpublished.

## Endpoints

1. GET /api/v1/leagues/:id/round_visibility

- Returns all visibility records for the league + season, including showcased user IDs.
- Query: season_id (optional; defaults to league.season_id)

2. PATCH /api/v1/leagues/:id/round_visibility_defaults

- Body: { default_visible_for_new_rounds: boolean }
- Updates the default visibility behavior for new rounds.

3. POST /api/v1/leagues/:id/rounds/:round_number/publish

- Publishes a specific round (idempotent).
- Body: { season_id?: number }
- Sets `visible: true`, `made_visible_by_user`, and `visible_at`.

4. POST /api/v1/leagues/:id/rounds/publish_bulk

- Body: { season_id?: number, round_numbers: number[] }
- Publishes multiple rounds at once.

5. GET /api/v1/leagues/:id/rounds/visibility_info

- Summarizes which rounds are published and what showcase data is available (Top-N / league-level)
- See docs/api/endpoints/rounds_visibility_info.md for schema

6. POST /api/v1/leagues/:id/rounds/:round_number/showcase_users

- Body: { season_id?: number, user_ids: number[] }
- Adds showcased users for a specific round (legacy; prefer league-level API). This respects league.showcase_user_limit.

7. DELETE /api/v1/leagues/:id/rounds/:round_number/showcase_users/:user_id

- Query: season_id (optional)
- Removes a single showcased user for a round (legacy).

8. GET /api/v1/leagues/:id/rounds/:round_number/users/:user_id/score

- Query: season_id (optional)
- Returns an individual user’s round score within a league scope.
- Visibility rules:
  - Owner/Admin: always allowed
  - If round is published: allowed
  - If not published: allowed if default_visible_for_new_rounds is true OR user is showcased for that round OR user is in the automatic top-N for that round

## Standings Behavior

- Leagues have scoring_visibility_mode: "always_visible" | "creator_controlled"
- For creator_controlled:
  - Non-owners/admins see standings computed from visible rounds only
  - Owners/admins see full standings
- For always_visible: standings are computed as before (full data)

## League Create/Update

- New attributes:
  - scoring_visibility_mode: "always_visible" | "creator_controlled"
  - showcase_user_limit?: number (positive integer)

## Errors

- 401 LEAGUE_OWNER_ONLY — Only the league owner can manage visibility
- 403 NOT_CONTENT_CREATOR — Owner must be a content creator to manage visibility
- 403 LEAGUE_ROUND_SCORES_HIDDEN — Round scores hidden due to visibility settings
- 403 USER_ROUND_SCORE_HIDDEN — Target user’s round score is hidden
- 404 LEAGUE_NOT_FOUND — Invalid league_id
- 404 NOT_FOUND — Round visibility or showcase not found
- 422 SHOWCASE_LIMIT_EXCEEDED — Attempt exceeds showcase_user_limit

## Notes

- Bulk operations are idempotent per round; publishing an already published round is safe.
- Default visibility setting applies only when no explicit record exists for a round.
- Round visibility is stored per season.
