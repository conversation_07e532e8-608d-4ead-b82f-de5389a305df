# League-level Showcased Users API

Purpose: Manage specific users showcased across all rounds of a league (independent from per-round Top-N).

- Owner-only, content-creator-only for mutations
- Does NOT count against `showcase_user_limit`

Endpoints

1) GET /api/v1/leagues/:id/showcased_users
- Response 200:
```
{ "data": { "league_id": number, "showcased_user_ids": number[] } }
```

2) POST /api/v1/leagues/:id/showcased_users
- Body: { user_ids: number[] }
- Response 200:
```
{ "data": { "league_id": number, "showcased_user_ids": number[] } }
```
- 400: `{ error_key: 'INVALID_PARAMS' }` if empty

3) DELETE /api/v1/leagues/:id/showcased_users/:user_id
- Response 200 same as GET

Notes
- These showcased users are visible to the league owner for all rounds (including unpublished). Regular users remain restricted by round publish state.

