# Round Scoring Endpoints

- OpenAPI: docs/api/round_scoring_openapi.yaml
- Contracts: docs/contracts/RoundScoring.ts
- Frontend examples: docs/examples/frontend/round-scoring.ts

## Summary
Retrieve scoring data for a specific round (matchday) across league members or for a specific user.

## Endpoints
- GET /api/v1/leagues/{id}/rounds/{round_number}/scores
- GET /api/v1/leagues/{id}/rounds/{round_number}/winners
- GET /api/v1/users/{id}/rounds/{round_number}/score

These endpoints are also available under /api/v1/youtube/... aliases.

## Visibility Rules
- League owners and admins can always view scores.
- If `round_scores_visible` is false, non-owners receive 403 (error_key: LEAGUE_ROUND_SCORES_HIDDEN).

## Error Handling
- 401 Unauthorized
- 403 Hidden by league configuration
- 404 Not found (where applicable)

## Notes
- Sorting: scores sorted by `total_points` descending.
- Winners: top 3 users by `total_points`.

