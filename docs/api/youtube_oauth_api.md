# YouTube OAuth API Documentation

## Overview

The YouTube OAuth API provides progressive OAuth authentication for YouTube integration, enabling users to connect their YouTube accounts with minimal initial permissions and upgrade to full YouTube scope when needed for content creator features.

## Base URL
```
http://localhost:3000/api/v1 (Development)
https://api.bragrights.football/api/v1 (Production)
```

## Authentication
All endpoints require Bearer token authentication except where noted.

```
Authorization: Bearer <access_token>
```

## Progressive OAuth Flow

### 1. Initial User Signup/Login
Users can sign up with minimal OAuth scope (openid, email, profile) without YouTube permissions.

### 2. YouTube Scope Upgrade
When users need YouTube features (content creator mode, subscription verification), they can upgrade their OAuth scope to include YouTube permissions.

### 3. Content Creator Verification
Users with 100+ subscribers can enable content creator mode and create subscriber-only leagues.

## API Endpoints

### GET /youtube_auth/status

Get current YouTube connection status and OAuth scope information.

**Authentication:** Required

**Response:**
```json
{
  "connected": boolean,
  "is_content_creator": boolean,
  "channel_id": string|null,
  "channel_name": string|null,
  "avatar_url": string|null,
  "subscriber_count": integer|null,
  "verified_at": string|null,
  "has_basic_scope": boolean,
  "has_youtube_scope": boolean,
  "needs_scope_upgrade": boolean,
  "can_use_youtube_features": boolean
}
```

**Example Response:**
```json
{
  "connected": true,
  "is_content_creator": false,
  "channel_id": "UC1234567890",
  "channel_name": "My Channel",
  "avatar_url": "https://example.com/avatar.jpg",
  "subscriber_count": 150,
  "verified_at": "2024-01-15T10:30:00Z",
  "has_basic_scope": true,
  "has_youtube_scope": true,
  "needs_scope_upgrade": false,
  "can_use_youtube_features": true
}
```

### GET /youtube_auth/scope_upgrade_url

Generate OAuth URL for progressive scope upgrade to include YouTube permissions.

**Authentication:** Required

**Parameters:**
- `redirect_uri` (required): OAuth callback URL

**Response:**
```json
{
  "oauth_url": "https://accounts.google.com/o/oauth2/v2/auth?...",
  "state": "32-character-hex-string",
  "scopes": ["openid", "email", "profile", "https://www.googleapis.com/auth/youtube.readonly"]
}
```

**Error Responses:**
- `400`: Missing redirect_uri or invalid OAuth configuration

### POST /youtube_auth/upgrade_scope

Complete OAuth scope upgrade after user authorization.

**Authentication:** Required

**Parameters:**
- `code` (required): Authorization code from OAuth callback
- `redirect_uri` (required): OAuth callback URL (must match)
- `state` (required): State parameter for CSRF protection

**Response:**
```json
{
  "success": true,
  "message": "OAuth scope upgraded successfully",
  "scopes": ["openid", "email", "profile", "https://www.googleapis.com/auth/youtube.readonly"]
}
```

**Error Responses:**
- `400`: Missing parameters, invalid state, or token exchange failure
- `500`: Internal server error during scope upgrade

### POST /youtube_auth/verify_subscription

Verify if user is subscribed to a specific YouTube channel.

**Authentication:** Required

**Parameters:**
- `channel_id` (required): YouTube channel ID to check subscription for

**Response:**
```json
{
  "status": "success",
  "connected": true,
  "subscribed": boolean,
  "message": "User is subscribed to this channel"
}
```

**Scope Upgrade Required Response:**
```json
{
  "status": "error",
  "connected": true,
  "subscribed": false,
  "message": "YouTube scope upgrade required",
  "needs_scope_upgrade": true
}
```

**Error Responses:**
- `400`: Missing channel_id parameter
- `401`: Authentication required

## YouTube Controller API

### GET /youtube/subscription_status

Check subscription status for a specific YouTube channel.

**Authentication:** Required

**Parameters:**
- `channel_id` (required): YouTube channel ID

**Response:**
```json
{
  "status": "success",
  "connected": true,
  "subscribed": boolean,
  "channel": {
    "id": "UC1234567890"
  }
}
```

### POST /youtube/update_creator_status

Enable or disable content creator mode for verified YouTube users.

**Authentication:** Required

**Prerequisites:**
- User must have YouTube account connected
- User must have YouTube OAuth scope
- User must have verified YouTube channel

**Response:**
```json
{
  "message": "Content creator status updated successfully",
  "is_content_creator": boolean
}
```

**Error Responses:**
- `400`: YouTube account not connected or scope upgrade required
- `422`: Failed to update creator status

### GET /youtube/check_subscriber_league_eligibility

Check if user is eligible to create subscriber-only leagues.

**Authentication:** Required

**Response:**
```json
{
  "eligible": boolean,
  "message": "You are eligible to create subscriber-only leagues",
  "subscriber_count": integer,
  "min_subscribers_required": 100
}
```

**Ineligible Response:**
```json
{
  "eligible": false,
  "message": "You need at least 100 subscribers to create a subscriber-only league",
  "subscriber_count": 50,
  "min_subscribers_required": 100
}
```

## Error Handling

### Standard Error Format
```json
{
  "error": "Error message description"
}
```

### Validation Error Format
```json
{
  "errors": ["Error message 1", "Error message 2"]
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (missing parameters, validation errors)
- `401`: Unauthorized (authentication required)
- `422`: Unprocessable Entity (business logic errors)
- `500`: Internal Server Error

## Progressive OAuth Implementation Notes

### Frontend Considerations

1. **Initial State**: Check user's OAuth status on app load
2. **Scope Upgrade Flow**: Guide users through scope upgrade when needed
3. **Error Handling**: Handle scope upgrade requirements gracefully
4. **State Management**: Track OAuth scope status in application state

### Security Considerations

1. **State Parameter**: Always validate state parameter in OAuth callbacks
2. **HTTPS Only**: Use HTTPS in production for OAuth redirects
3. **Token Storage**: Store tokens securely on backend only
4. **Scope Validation**: Verify user has required scope before API calls

### Business Logic Flow

1. User signs up with basic OAuth scope
2. User attempts to use YouTube features
3. System detects missing YouTube scope
4. User is prompted to upgrade OAuth scope
5. User completes OAuth upgrade flow
6. User can now access YouTube features
7. Content creators can verify channel and create subscriber leagues
