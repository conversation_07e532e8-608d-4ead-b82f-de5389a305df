# OAuth Scope Reduction API

PATCH /api/v1/youtube_auth/reduce_scope

- Auth: Bearer token required
- One-way operation: Only allowed to reduce from youtube.readonly to BASIC_SCOPES (openid, email, profile). Re-expansion requires a full re-auth (upgrade flow).

## Request

```json
{ "scopes": ["openid", "email", "profile"] }
```

Validation rules:
- scopes must be exactly ["openid","email","profile"]
- user must currently have youtube.readonly scope in their stored credentials

## Responses

200 OK
```json
{ "success": true, "scopes": ["openid", "email", "profile"] }
```

400 Bad Request (missing scopes):
```json
{ "error": "Missing scopes parameter" }
```

422 Unprocessable Entity (invalid policy):
```json
{ "error": "Only allowed to reduce from youtube.readonly to BASIC_SCOPES (openid, email, profile)" }
```

## TypeScript Contracts

```ts
export interface ReduceScopeRequest { scopes: ['openid','email','profile'] }
export interface ReduceScopeResponse { success: true; scopes: ['openid','email','profile'] }
```

## curl Example

```bash
curl -X PATCH \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"scopes":["openid","email","profile"]}' \
  "$API_URL/api/v1/youtube_auth/reduce_scope"
```

## Security considerations
- This endpoint does not perform token revocation; it updates the stored scope to limit backend operations.
- For stricter privacy, users can also disconnect entirely via disconnect.
- Re-expansion is only via the upgrade_scope flow (fresh consent prompt).

