# YouTube OAuth Connect API Documentation

## Overview
This document describes the YouTube OAuth integration endpoints for connecting user accounts to their YouTube channels.

## Authentication
All endpoints except `callback`, `login`, and `signup` require authentication via the `Authorization` header:
```
Authorization: Bearer <access_token>
```

## Endpoints

### POST /api/v1/youtube_auth/connect

Connects a YouTube account to the authenticated user's account.

#### Request Parameters

The endpoint supports two flows:

**OAuth Flow (Recommended for Production):**
- `code` (string, required): OAuth authorization code from Google
- `redirect_uri` (string, required): The redirect URI used in the OAuth flow
- `client_id` (string, required): Google OAuth client ID

**Direct Channel Data Flow (Development/Testing):**
- `channel_id` (string, required): YouTube channel ID
- `channel_name` (string, required): YouTube channel name
- `avatar_url` (string, optional): Channel avatar URL
- `subscriber_count` (integer, optional): Channel subscriber count
- `credentials` (object, optional): OAuth credentials object

#### Request Example (OAuth Flow)
```json
{
  "code": "4/0AX4XfWh...",
  "redirect_uri": "http://localhost:3000/youtube/callback",
  "client_id": "*********-abcdef.apps.googleusercontent.com"
}
```

#### Success Response (200 OK)
```json
{
  "message": "YouTube account connected successfully",
  "user": {
    "id": 123,
    "youtube_channel_id": "UCxxxxxxxxxxxxxxxxxxxxx",
    "youtube_channel_name": "Channel Name",
    "is_content_creator": false
  }
}
```

#### Error Responses

**400 Bad Request - Missing Parameters:**
```json
{
  "error": "Missing required parameters: code, redirect_uri"
}
```

**400 Bad Request - Invalid OAuth Configuration:**
```json
{
  "error": "Invalid OAuth client configuration"
}
```

**400 Bad Request - Token Exchange Failed:**
```json
{
  "error": "Failed to exchange authorization code",
  "details": "invalid_grant",
  "status_code": 400
}
```

**400 Bad Request - No YouTube Channel:**
```json
{
  "error": "No YouTube channel found for this account"
}
```

**409 Conflict - Channel Already Connected:**
```json
{
  "error": "This YouTube channel is already connected to another account (<EMAIL>)",
  "details": "YouTube channel already connected to another account",
  "status": 409
}
```

**403 Forbidden - Feature Disabled:**
```json
{
  "error": "YouTube Connect is not available at this time"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Unexpected error during YouTube API call: ..."
}
```

### GET /api/v1/youtube_auth/status

Returns the current YouTube connection status for the authenticated user.

#### Success Response (200 OK)
```json
{
  "connected": true,
  "is_content_creator": false,
  "channel_id": "UCxxxxxxxxxxxxxxxxxxxxx",
  "channel_name": "Channel Name",
  "avatar_url": "https://yt3.ggpht.com/...",
  "subscriber_count": 1234,
  "verified_at": "2023-12-01T10:00:00Z"
}
```

### POST /api/v1/youtube_auth/disconnect

Disconnects the YouTube account from the authenticated user's account.

#### Success Response (200 OK)
```json
{
  "message": "YouTube account disconnected successfully"
}
```

## Environment Variables Required

The following environment variables must be set for the YouTube OAuth integration to work:

- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `YOUTUBE_API_KEY`: YouTube Data API key
- `YOUTUBE_OAUTH_CALLBACK_URL`: OAuth callback URL for validation
- `YOUTUBE_CONNECT_ENABLED`: Feature flag (defaults to 'true' in non-production, 'false' in production)

## Frontend Integration Notes

1. **OAuth Flow**: The frontend should initiate the Google OAuth flow and obtain an authorization code, then send it to the `/connect` endpoint along with the redirect URI and client ID.

2. **Error Handling**: The frontend should handle all the error cases listed above and provide appropriate user feedback.

3. **Status Checking**: Use the `/status` endpoint to check if a user already has a connected YouTube account.

4. **Feature Flag**: The connect functionality may be disabled via the `YOUTUBE_CONNECT_ENABLED` environment variable. Check for 403 responses and handle accordingly.

5. **Conflict Resolution**: If a user tries to connect a YouTube channel that's already connected to another account, provide clear messaging and potentially offer account merging options.

## Security Considerations

- OAuth client ID and redirect URI are validated against backend configuration
- YouTube channels can only be connected to one user account at a time
- All API calls to Google services are properly authenticated and logged
- Sensitive credential data is not logged or exposed in error messages
