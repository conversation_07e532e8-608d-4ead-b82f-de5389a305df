# Standings API Documentation

This document describes the League and Competition Standings API endpoints for the BragRights application.

## Endpoints

### Get League Standings

**Endpoint:** `GET /api/v1/leagues/:id/standings`

**Description:** Returns the current standings for a league.

**Authentication:** Required

**URL Parameters:**

- `:id` - The ID of the league

**Query Parameters:**

- `season_id` (optional) - The ID of a specific season to get historical standings for

**Response Format:**

```json
{
  "league": {
    "id": 1,
    "name": "My League",
    "season": {
      "id": 1,
      "start_date": "2023-08-01",
      "end_date": "2024-05-31"
    },
    "standings": [
      {
        "username": "user1",
        "points": 30,
        "correct": 10,
        "incorrect": 5,
        "perfect": 5,
        "position": 1,
        "top_message": "🏆 user1 is leading the league! Congratulations!"
      },
      {
        "username": "user2",
        "points": 25,
        "correct": 8,
        "incorrect": 7,
        "perfect": 4,
        "position": 2,
        "top_message": "🥈 user2 is in second place. Keep pushing!"
      },
      {
        "username": "user3",
        "points": 20,
        "correct": 7,
        "incorrect": 8,
        "perfect": 3,
        "position": 3,
        "top_message": "🥉 user3 is in third place. Great job!"
      },
      {
        "username": "user4",
        "points": 15,
        "correct": 5,
        "incorrect": 10,
        "perfect": 2,
        "position": 4,
        "top_message": null
      }
    ]
  },
  "message": ["League standings calculated"],
  "status": 200,
  "type": "success"
}
```

### Get Competition Standings

**Endpoint:** `GET /api/v1/competitions/:id/standings`

**Description:** Returns the standings for all users who have made predictions in a competition.

**Authentication:** Required

**URL Parameters:**

- `:id` - The ID of the competition

**Query Parameters:**

- `season_id` (optional) - The ID of a specific season to get historical standings for

**Response Format:**

```json
{
  "competition": {
    "id": 1,
    "name": "Premier League",
    "season": {
      "id": 1,
      "start_date": "2023-08-01",
      "end_date": "2024-05-31"
    },
    "standings": [
      {
        "username": "user1",
        "points": 30,
        "correct": 10,
        "incorrect": 5,
        "perfect": 5,
        "position": 1,
        "top_message": "🏆 user1 is leading the competition! Congratulations!"
      },
      {
        "username": "user2",
        "points": 25,
        "correct": 8,
        "incorrect": 7,
        "perfect": 4,
        "position": 2,
        "top_message": "🥈 user2 is in second place. Keep pushing!"
      },
      {
        "username": "user3",
        "points": 20,
        "correct": 7,
        "incorrect": 8,
        "perfect": 3,
        "position": 3,
        "top_message": "🥉 user3 is in third place. Great job!"
      },
      {
        "username": "user4",
        "points": 15,
        "correct": 5,
        "incorrect": 10,
        "perfect": 2,
        "position": 4,
        "top_message": null
      }
    ]
  },
  "message": ["Competition standings calculated"],
  "status": 200,
  "type": "success"
}
```

## Historical Standings

To retrieve standings from a previous season, include the `season_id` query parameter:

```
GET /api/v1/leagues/:id/standings?season_id=2
GET /api/v1/competitions/:id/standings?season_id=2
```

This will return the standings for the specified historical season.

## Frontend Implementation

The frontend should:

1. Call the standings endpoint to get the current standings for a league
2. Display the standings in a table, sorted by position
3. Show special messages for the top 3 users
4. Provide a dropdown or tabs to select historical seasons if available
5. When a historical season is selected, call the API with the `season_id` parameter

### TypeScript Interface

```typescript
interface Standing {
  username: string;
  points: number;
  correct: number;
  incorrect: number;
  perfect: number;
  position: number;
  top_message?: string;
}

interface LeagueStandingsResponse {
  league: {
    id: number;
    name: string;
    season?: {
      id: number;
      start_date: string;
      end_date: string;
    };
    standings: Standing[];
  };
  message: string[];
  status: number;
  type: string;
}

interface CompetitionStandingsResponse {
  competition: {
    id: number;
    name: string;
    season?: {
      id: number;
      start_date: string;
      end_date: string;
    };
    standings: Standing[];
  };
  message: string[];
  status: number;
  type: string;
}

export const getLeagueStandings = async (
  leagueId: number,
  seasonId?: number
) => {
  const params = seasonId ? { season_id: seasonId } : {};
  const response = await api.get<LeagueStandingsResponse>(
    `/api/v1/leagues/${leagueId}/standings`,
    { params }
  );
  return response.data;
};

export const getCompetitionStandings = async (
  competitionId: number,
  seasonId?: number
) => {
  const params = seasonId ? { season_id: seasonId } : {};
  const response = await api.get<CompetitionStandingsResponse>(
    `/api/v1/competitions/${competitionId}/standings`,
    { params }
  );
  return response.data;
};
```

## Top 3 User Messages

Both the league and competition standings APIs include special messages for the top 3 users:

1. **First Place**: Users in first place receive a trophy emoji and a congratulatory message
2. **Second Place**: Users in second place receive a silver medal emoji and an encouraging message
3. **Third Place**: Users in third place receive a bronze medal emoji and a positive message

These messages are automatically included in the API response for both current and historical standings in the `top_message` field. Users beyond the top 3 positions will have a `null` value for this field.

Example messages for league standings:

- 1st place: "🏆 username is leading the league! Congratulations!"
- 2nd place: "🥈 username is in second place. Keep pushing!"
- 3rd place: "🥉 username is in third place. Great job!"

Example messages for competition standings:

- 1st place: "🏆 username is leading the competition! Congratulations!"
- 2nd place: "🥈 username is in second place. Keep pushing!"
- 3rd place: "🥉 username is in third place. Great job!"

The frontend should display these messages prominently when showing standings to enhance user engagement and gamification.
