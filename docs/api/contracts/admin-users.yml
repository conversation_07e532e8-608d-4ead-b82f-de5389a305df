# Admin Users API Contract
resource: Admin Users
basePath: /api/v1/admin/users

endpoints:
  - method: GET
    path: /
    request:
      query:
        email: string?
        username: string?
        status: enum[active, deleted, all]?
        page: integer?
        per_page: integer?
    response:
      200:
        body:
          data: AdminUser[]
          pagination: PaginationMeta
  - method: GET
    path: /{id}
    request:
      params:
        id: integer
    response:
      200:
        body:
          data: AdminUser
          memberships: { league_id: integer, league_name: string, joined_at: string }[]
          predictions_count: integer
  - method: PATCH
    path: /{id}
    request:
      params:
        id: integer
      body:
        user:
          email: string?
          username: string?
          admin: boolean?
    response:
      200:
        body:
          data: AdminUser
  - method: DELETE
    path: /{id}
    request:
      params:
        id: integer
    response:
      200:
        body:
          message: string
  - method: POST
    path: /{id}/restore
    request:
      params:
        id: integer
    response:
      200:
        body:
          message: string
  - method: POST
    path: /{id}/toggle_admin
    request:
      params:
        id: integer
    response:
      200:
        body:
          data: AdminUser

schemas:
  AdminUser:
    id: integer
    email: string
    username: string
    admin: boolean
    is_content_creator: boolean?
    youtube_channel_id: string?
    youtube_channel_name: string?
    youtube_subscriber_count: integer?
  PaginationMeta:
    page: integer
    per_page: integer
    total_pages: integer
    total_count: integer

