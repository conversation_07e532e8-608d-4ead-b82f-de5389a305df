# Admin Leagues API Contract
resource: Admin Leagues
basePath: /api/v1/admin/leagues

endpoints:
  - method: GET
    path: /
    request:
      query:
        page: integer?
        per_page: integer?
        q: string?
        archived: boolean?
    response:
      200:
        body:
          data: AdminLeague[]
          pagination: PaginationMeta
  - method: GET
    path: /{id}
    request:
      params:
        id: integer
    response:
      200:
        body:
          data: AdminLeague
  - method: PUT
    path: /{id}
    request:
      params:
        id: integer
      body:
        league: AdminLeagueUpdate
    response:
      200:
        body:
          data: AdminLeague
  - method: DELETE
    path: /{id}
    request:
      params:
        id: integer
    response:
      200:
        body:
          message: string
  - method: POST
    path: /{id}/transfer_ownership
    request:
      params:
        id: integer
      body:
        transfer:
          new_owner_id: integer
    response:
      200:
        body:
          message: string
          data: AdminLeague
  - method: POST
    path: /bulk_action
    request:
      body:
        ids: integer[]
        action_name: enum[archive, unarchive]
    response:
      200:
        body:
          action: string
          processed: { id: integer, status: string }[]
          errors: { id: integer, error: string }[]

schemas:
  AdminLeague:
    id: integer
    name: string
    open: boolean
    archived: boolean
    archived_at: string?
    starting_matchday: integer?
    youtube_league: boolean
    subscriber_only: boolean
    youtube_channel_id: string?
    owner: AdminUser
    competition: { id: integer, name: string, code: string }?
    season: { id: integer, start_date: string?, end_date: string? }?
    member_count: integer
    created_at: string
    updated_at: string
  AdminLeagueUpdate:
    name: string?
    open: boolean?
    competition_id: integer?
    season_id: integer?
    starting_matchday: integer?
    youtube_league: boolean?
    subscriber_only: boolean?
    youtube_channel_id: string?
    subscriber_requirement_type: string?
    min_subscriber_date: string?
  AdminUser:
    id: integer
    email: string
    username: string
    admin: boolean
  PaginationMeta:
    page: integer
    per_page: integer
    total_pages: integer
    total_count: integer

