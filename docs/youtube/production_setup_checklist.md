# YouTube Connection Production Setup Checklist

This checklist outlines the necessary steps to properly configure YouTube integration on a production server.

## Environment Variables

Set the following environment variables on your production server:

```bash
# YouTube/Google API credentials
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
YOUTUBE_API_KEY=your_api_key
YOUTUBE_OAUTH_CALLBACK_URL=https://your-domain.com/auth/youtube/callback

# Encryption keys (generate with rails secret)
ENCRYPTION_PRIMARY_KEY=your_primary_key
ENCRYPTION_DETERMINISTIC_KEY=your_deterministic_key
ENCRYPTION_KEY_DERIVATION_SALT=your_key_derivation_salt
```

## Code Changes

1. Enable encryption in User model:
   - Uncomment `encrypts :youtube_credentials` in app/models/user.rb

2. Configure Active Record Encryption in production.rb:
   ```ruby
   config.active_record.encryption.primary_key = ENV.fetch('ENCRYPTION_PRIMARY_KEY')
   config.active_record.encryption.deterministic_key = ENV.fetch('ENCRYPTION_DETERMINISTIC_KEY')
   config.active_record.encryption.key_derivation_salt = ENV.fetch('ENCRYPTION_KEY_DERIVATION_SALT')
   ```

## Service Configuration

1. Ensure Redis is running (for caching and quota tracking):
   ```bash
   redis-cli ping
   ```

2. Verify Sidekiq configuration for YouTube jobs:
   ```bash
   sudo systemctl restart sidekiq
   sudo systemctl status sidekiq
   ```

## Data Migration

If you have existing YouTube credentials in your database, migrate them to the encrypted format:

```bash
rails runner "User.where.not(youtube_credentials: nil).find_each do |user|; creds = JSON.parse(user.youtube_credentials); user.update_column(:youtube_credentials, nil); user.update(youtube_credentials: creds.to_json); end"
```

## Testing

Test the YouTube API connection:

```bash
rails runner scripts/test_youtube_api.rb USER_ID
```

## Verification

To verify that encryption is working correctly:

```ruby
# In Rails console
user = User.find_by(email: "<EMAIL>")

# Check if credentials are encrypted
encrypted_value = user.attributes_before_type_cast["youtube_credentials"]
puts "Credentials are encrypted: #{encrypted_value.include?('--encrypted--')}"

# Check if decryption works
begin
  decrypted = user.youtube_credentials
  creds = JSON.parse(decrypted)
  puts "Decryption successful: #{creds['access_token'].present?}"
rescue => e
  puts "Decryption failed: #{e.message}"
end
```