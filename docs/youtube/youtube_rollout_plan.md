# YouTube Integration Rollout Plan (Basic now, YouTube later)

This document describes what we ship now and how we enable the YouTube Connect and Creator features later without causing the Google "unverified app" warning during signup/login.

## Current state (ship now)

- Signup/Login with Google is visible and enabled
- Signup/Login flow uses only basic scopes: openid email profile
- Users can sign up with email or Google (basic scopes) interchangeably
- Connecting a YouTube channel and Creator features are deferred
- Backend gates are in place so Connect/Creator endpoints return 403 in prod

Environment flags

- YOUTUBE_CONNECT_ENABLED=false in production (true in dev by default)
- YOUTUBE_CREATOR_ENABLED=false in production (true in dev by default)

Frontend specifics

- initiateYoutubeAuth(mode)
  - mode = "signup" | "login"  requests only basic scopes
  - mode = "connect"  requests youtube.readonly but only when FT_youtubeConnect is enabled
- Feature toggles
  - FT_youtubeConnect: controls whether connect flow is available (defaults enabled in dev only)
  - FT_youtubeContentCreator: controls whether Creator UI and features are visible/usable

Backend expectations

- Endpoints for /auth/youtube/signup and /auth/youtube/login accept code and return app tokens; no reliance on youtube.readonly
- /youtube_auth/connect links channel when FE later requests incremental auth

## What remains OFF (until verified)

- The Connect YouTube flow in production (keep FT_youtubeConnect OFF)
- Any subscriber-only league enforcement
- Any BE YouTube API calls on non-connected users

## Before enabling the YouTube Connect feature

Checklist

1. Google verification completed for youtube.readonly scope
2. BE endpoints in place: connect/disconnect/status/update_creator_status
3. Admin toggles reviewed
4. Comms ready (release notes / in-app hints)

Switch-on procedure

- Enable FT_youtubeConnect in production
- (Optional) Soft-launch: limit visibility to admin/test cohort by gating UI routes or checking roles
- Monitor logs for 409 conflicts (already-connected channel) and error rates

## Enabling Content Creator features later

Prereqs

- FT_youtubeConnect already ON for some time
- Eligibility endpoint implemented (optional) to guide users

Steps

- Enable FT_youtubeContentCreator in production
- Expose creator toggle in profile (already wired)
- If enforcing minimum subscribers, backend should return 422 with explanation; FE shows tooltip/error state

## Future additions (not required now)

- Subscriber verification endpoints (verify_subscription, subscription_status)
- Subscriber-only leagues creation and enforcement
- Quota monitoring and caching for cost control
- Members-only leagues (requires separate approval)

## Risks and mitigations

- Risk: Users see unverified prompts
  - Mitigation: signup/login stays basic; connect uses youtube.readonly only after verification
- Risk: Users confused by YouTube buttons before connect enabled
  - Mitigation: Buttons remain for basic Google identity login; wording clarifies login with Google/YouTube. Connecting the channel is separate in Profile.
- Risk: Scope sprawl
  - Mitigation: Only request youtube.readonly in connect; include_granted_scopes enabled to support incremental
