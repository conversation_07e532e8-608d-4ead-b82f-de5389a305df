# Backend Guide: YouTube OAuth and Feature Rollout

This document defines the backend contract and expectations so the frontend can:

- Let users sign up / log in with Google (basic scopes only)
- Let authenticated users optionally connect their YouTube channel later (incremental auth with youtube.readonly)
- Gate "creator" features behind feature toggles

No Rails-specific assumptions; keep the BE technology-agnostic.

## 1) OAuth Scopes and Flows

We use two distinct flows:

- Basic auth (signup/login): openid email profile

  - Purpose: authenticate user account only
  - User sees no YouTube data permission prompts
  - Frontend initiates Google OAuth with only these scopes

- Connect YouTube (incremental auth): youtube.readonly + openid email profile
  - Purpose: allow access to the users YouTube channel info and verify subscriptions
  - Frontend requests this scope ONLY when the user explicitly starts the Connect YouTube flow and when the feature toggle is enabled

Important: Do not fail signup/login if YouTube scopes are missing. Treat connect as a separate, optional step.

## 2) Environment variables

- BACK_END_URL (used by FE to call BE)
- GOOGLE_CLIENT_ID / GOOGLE_CLIENT_SECRET (BE side to exchange auth code)
- YOUTUBE_OAUTH_CALLBACK_URL (must match FE redirect)
- YOUTUBE_API_KEY (if B<PERSON> calls YouTube Data API directly)
- YOUTUBE_CONNECT_ENABLED (default false in prod; gates connect, disconnect, verify_subscription)
- YOUTUBE_CREATOR_ENABLED (default false in prod; gates creator endpoints)

## 3) Endpoints and contracts

All endpoints are under your existing BE base URL. Request and response bodies are JSON.

1. POST /api/v1/auth/youtube/signup

- Input: { code, redirect_uri, client_id }
- Action: Exchange code with Google (basic scopes)  create user (or link), issue your app tokens
- Output (TokenResponse-like):
  {
  "token": "<access_token>",
  "refresh_token": "<refresh_token>",
  "expires_in": 86400,
  "token_type": "Bearer",
  "resource_owner": { "id": 123, "email": "<EMAIL>" },
  "admin": false
  }
- Errors: 400 (bad request), 401/403 (policy), 5xx

2. POST /api/v1/auth/youtube/login

- Same contract as signup but for existing accounts

3. POST /api/v1/youtube_auth/connect (Authenticated)

- Input: { code, redirect_uri, client_id }
- Action:
  - Exchange code for tokens including youtube.readonly
  - Fetch and persist channel info (id, name) as needed
  - Store refresh token if BE will call YouTube APIs later
- Output: { success: true, channel_id, channel_name }
- Errors:
  - 409 if channel already linked to a different user (message should include that users email if possible; FE has special handling)
  - 400/401/403/5xx otherwise

4. POST /api/v1/youtube_auth/disconnect (Authenticated)

- Action: unlink channel from the user
- Output: { success: true }

5. GET /api/v1/youtube_auth/status (Authenticated)

- Output:
  {
  "connected": true|false,
  "channel_id": "UC..." | null,
  "channel_name": "..." | null,
  "is_content_creator": true|false
  }
- Note: If not implemented yet, 404 is acceptable; FE treats it as not connected.

6. POST /api/v1/youtube/update_creator_status (Authenticated)

- Input: { is_content_creator: boolean }
- Action: toggle creator mode for the user (optional policy checks)
- Output: { success: true }
- Errors: 422 if eligibility not met (FE shows helpful tooltip)

7. Optional, for subscriber-only leagues (future)

- POST /api/v1/youtube_auth/verify_subscription { channel_id }
- GET /api/v1/youtube/subscription_status?channel_id=...
- GET /api/v1/youtube/check_subscriber_league_eligibility

## 4) Token exchange details

- For signup/login: FE only requests basic scopes. Exchange code for id_token/userinfo and proceed with your normal account creation/login. Do not require YouTube scopes here.
- For connect: FE requests youtube.readonly. Exchange the code and persist channel metadata and refresh token (if the BE will poll/verify).

## 5) Error handling

Return concise, stable errors. FE has special UI handling for:

- 409 Conflict with message containing "already connected to another account" (FE parses an email in parentheses, if present)
- 422 Unprocessable Entity for eligibility failures

## 6) Security

- Store Google/YouTube tokens server-side only; FE uses app tokens via HttpOnly cookie
- Validate redirect_uri and client_id match your Google OAuth client
- Use HTTPS everywhere; set proper CORS for FE origin

## 7) Minimal data persisted

- On connect: youtube_channel_id, youtube_channel_name, user_id linkage, timestamps
- Optional: refresh_token if the BE will call YouTube on behalf of the user

## 8) Testing checklist

- Signup/Login with basic scopes only (no YouTube prompts)
- Connect flow requests YouTube scope and links channel
- Disconnect removes channel linkage
- Status reflects connection state
- Creator status toggles correctly and enforces any rules

## 9) Rollout interop

- While the app is unverified for YouTube scopes, keep connect endpoints implemented but behind FE feature toggle; FE wont initiate connect until enabled.
- After YouTube scope verification completes, no BE changes are required; FE will start calling connect.
