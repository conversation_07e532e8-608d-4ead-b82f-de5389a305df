// docs/contracts/UserService.ts

/**
 * User Service API Contract
 * 
 * This contract defines all interfaces and types for user-related API endpoints.
 * Frontend teams should import and use these types for type safety.
 */

// Base types
export type UserId = string; // UUID v4
export type UserRole = 'admin' | 'user' | 'moderator';
export type UserStatus = 'active' | 'inactive' | 'suspended';

// Request interfaces
export interface CreateUserRequest {
  email: string;          // Required: valid email format
  name: string;           // Required: 1-100 characters, alphanumeric + spaces
  role?: UserRole;        // Optional: defaults to 'user'
  department?: string;    // Optional: 1-50 characters
}

export interface UpdateUserRequest {
  name?: string;          // Optional: 1-100 characters
  role?: UserRole;        // Optional: admin only
  status?: UserStatus;    // Optional: admin only
  department?: string;    // Optional: 1-50 characters
}

export interface GetUsersQuery {
  page?: number;          // Optional: default 1, min 1
  limit?: number;         // Optional: default 20, max 100
  role?: UserRole;        // Optional: filter by role
  status?: UserStatus;    // Optional: filter by status
  search?: string;        // Optional: search in name/email
  sortBy?: 'name' | 'email' | 'createdAt'; // Optional: default 'createdAt'
  sortOrder?: 'asc' | 'desc'; // Optional: default 'desc'
}

// Response interfaces
export interface User {
  id: UserId;
  email: string;          // Normalized lowercase email
  name: string;           // Sanitized name
  role: UserRole;
  status: UserStatus;
  department: string | null;
  createdAt: string;      // ISO 8601 timestamp
  updatedAt: string;      // ISO 8601 timestamp
  lastLoginAt: string | null; // ISO 8601 timestamp or null
}

export interface CreateUserResponse {
  data: User;
  success: true;
  message: 'User created successfully';
}

export interface GetUserResponse {
  data: User;
  success: true;
}

export interface GetUsersResponse {
  data: User[];
  success: true;
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UpdateUserResponse {
  data: User;
  success: true;
  message: 'User updated successfully';
}

export interface DeleteUserResponse {
  success: true;
  message: 'User deleted successfully';
}

// Error interfaces
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ApiError {
  success: false;
  message: string;
  errors?: ValidationError[];
  code: string;
}

// Combined response types for frontend
export type UserApiResponse =
  | CreateUserResponse
  | GetUserResponse
  | UpdateUserResponse
  | DeleteUserResponse
  | ApiError;

export type UsersListResponse = GetUsersResponse | ApiError;

// Frontend utility types
export interface UserFormData extends Omit<CreateUserRequest, 'role'> {
  role: UserRole; // Make role required in forms
}

export interface UserUpdateFormData extends UpdateUserRequest {
  id: UserId; // Include ID for updates
}

// API endpoints enum for type safety
export enum UserEndpoints {
  CREATE = '/api/users',
  GET_ALL = '/api/users',
  GET_BY_ID = '/api/users/:id',
  UPDATE = '/api/users/:id',
  DELETE = '/api/users/:id',
}

// HTTP status codes for this service
export enum UserHttpStatus {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
}