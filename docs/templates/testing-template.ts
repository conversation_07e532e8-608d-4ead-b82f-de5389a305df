// docs/templates/testing-template.ts

/**
 * Comprehensive Testing Template
 * 
 * This template shows how to structure tests with proper mocking
 * for endpoints, services, and models following our testing standards.
 */

// @ts-nocheck
import { jest } from '@jest/globals';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { UserService } from '../services/UserService';
import { User, CreateUserRequest } from '../contracts/UserService';

// Mock environment variables
const mockEnv = {
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test_db',
  JWT_SECRET: 'test-jwt-secret-key',
  API_BASE_URL: 'https://api.test.example.com',
  REDIS_URL: 'redis://localhost:6379/1',
  EMAIL_API_KEY: 'test-email-api-key',
  AWS_REGION: 'us-east-1',
  NODE_ENV: 'test'
};

// MSW Server for mocking external API calls
const server = setupServer(
  // Mock external user validation API
  rest.get('https://api.external.com/validate-email/:email', (req, res, ctx) => {
    const { email } = req.params;
    return res(
      ctx.json({
        valid: email.includes('@'),
        disposable: false,
        domain: email.split('@')[1]
      })
    );
  }),

  // Mock email service API
  rest.post('https://email-service.com/send', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ messageId: 'mock-message-id-123' })
    );
  }),

  // Mock AWS S3 operations
  rest.put('https://s3.amazonaws.com/*', (req, res, ctx) => {
    return res(ctx.status(200));
  })
);

// Test data factories
class UserFactory {
  static createUserRequest(overrides: Partial<CreateUserRequest> = {}): CreateUserRequest {
    return {
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      department: 'Engineering',
      ...overrides
    };
  }

  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: 'test-user-id-123',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      status: 'active',
      department: 'Engineering',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      lastLoginAt: '2024-01-01T00:00:00Z',
      ...overrides
    };
  }

  static createUsers(count: number): User[] {
    return Array.from({ length: count }, (_, i) =>
      this.createUser({
        id: `test-user-id-${i}`,
        email: `test${i}@example.com`,
        name: `Test User ${i}`
      })
    );
  }
}

// Database mocking
const mockDatabase = {
  user: {
    create: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn()
  },
  $transaction: jest.fn()
};

// Service mocking utilities
const mockExternalServices = {
  emailService: {
    sendWelcomeEmail: jest.fn().mockResolvedValue({ messageId: 'mock-123' }),
    sendPasswordReset: jest.fn().mockResolvedValue({ messageId: 'mock-456' })
  },

  auditService: {
    logUserAction: jest.fn().mockResolvedValue(undefined)
  },

  cacheService: {
    get: jest.fn(),
    set: jest.fn().mockResolvedValue(undefined),
    delete: jest.fn().mockResolvedValue(undefined)
  }
};

describe('UserService', () => {
  let userService: UserService;

  // Setup before all tests
  beforeAll(() => {
    // Start MSW server
    server.listen({ onUnhandledRequest: 'error' });

    // Set environment variables
    Object.assign(process.env, mockEnv);
  });

  // Setup before each test
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Reset database mocks
    Object.values(mockDatabase.user).forEach(mock => mock.mockReset());
    mockDatabase.$transaction.mockReset();

    // Reset external service mocks
    Object.values(mockExternalServices).forEach(service => {
      Object.values(service).forEach(mock => (mock as jest.Mock).mockReset?.());
    });

    // Create fresh service instance with mocked dependencies
    userService = new UserService({
      database: mockDatabase,
      emailService: mockExternalServices.emailService,
      auditService: mockExternalServices.auditService,
      cacheService: mockExternalServices.cacheService
    });
  });

  // Cleanup after each test
  afterEach(() => {
    server.resetHandlers();
  });

  // Cleanup after all tests
  afterAll(() => {
    server.close();
    // Reset environment
    Object.keys(mockEnv).forEach(key => {
      delete process.env[key];
    });
  });

  describe('createUser', () => {
    it('should create a user with valid data', async () => {
      // Arrange
      const createUserRequest = UserFactory.createUserRequest();
      const expectedUser = UserFactory.createUser();

      mockDatabase.user.create.mockResolvedValue(expectedUser);
      mockDatabase.user.findUnique.mockResolvedValue(null); // Email not exists

      // Act
      const result = await userService.createUser(createUserRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(expectedUser);
      expect(mockDatabase.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: createUserRequest.email,
          name: createUserRequest.name,
          role: createUserRequest.role
        })
      });
      expect(mockExternalServices.emailService.sendWelcomeEmail).toHaveBeenCalledWith(expectedUser);
      expect(mockExternalServices.auditService.logUserAction).toHaveBeenCalledWith(
        'USER_CREATED',
        expect.any(Object)
      );
    });

    it('should return error when email already exists', async () => {
      // Arrange
      const createUserRequest = UserFactory.createUserRequest();
      const existingUser = UserFactory.createUser();

      mockDatabase.user.findUnique.mockResolvedValue(existingUser);

      // Act
      const result = await userService.createUser(createUserRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Email already exists');
      expect(mockDatabase.user.create).not.toHaveBeenCalled();
      expect(mockExternalServices.emailService.sendWelcomeEmail).not.toHaveBeenCalled();
    });

    it('should handle database transaction failures', async () => {
      // Arrange
      const createUserRequest = UserFactory.createUserRequest();

      mockDatabase.user.findUnique.mockResolvedValue(null);
      mockDatabase.$transaction.mockRejectedValue(new Error('Transaction failed'));

      // Act
      const result = await userService.createUser(createUserRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Database error');
      expect(mockExternalServices.emailService.sendWelcomeEmail).not.toHaveBeenCalled();
    });

    it('should handle external email service failures gracefully', async () => {
      // Arrange
      const createUserRequest = UserFactory.createUserRequest();
      const expectedUser = UserFactory.createUser();

      mockDatabase.user.create.mockResolvedValue(expectedUser);
      mockDatabase.user.findUnique.mockResolvedValue(null);
      mockExternalServices.emailService.sendWelcomeEmail.mockRejectedValue(
        new Error('Email service unavailable')
      );

      // Act
      const result = await userService.createUser(createUserRequest);

      // Assert
      expect(result.success).toBe(true); // Should still succeed
      expect(result.data).toEqual(expectedUser);
      // Should log the email failure but not fail the user creation
    });
  });

  describe('getUserById', () => {
    it('should return user when found', async () => {
      // Arrange
      const userId = 'test-user-id';
      const expectedUser = UserFactory.createUser({ id: userId });

      // Mock cache miss, then database hit
      mockExternalServices.cacheService.get.mockResolvedValue(null);
      mockDatabase.user.findUnique.mockResolvedValue(expectedUser);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(expectedUser);
      expect(mockExternalServices.cacheService.set).toHaveBeenCalledWith(
        `user:${userId}`,
        expectedUser,
        expect.any(Number)
      );
    });

    it('should return cached user when available', async () => {
      // Arrange
      const userId = 'test-user-id';
      const cachedUser = UserFactory.createUser({ id: userId });

      mockExternalServices.cacheService.get.mockResolvedValue(cachedUser);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(cachedUser);
      expect(mockDatabase.user.findUnique).not.toHaveBeenCalled();
    });
  });

  describe('Environment Variable Handling', () => {
    it('should handle missing required environment variables', () => {
      // Arrange
      const originalEnv = process.env.DATABASE_URL;
      delete process.env.DATABASE_URL;

      // Act & Assert
      expect(() => new UserService()).toThrow('DATABASE_URL is required');

      // Cleanup
      process.env.DATABASE_URL = originalEnv;
    });

    it('should use default values for optional environment variables', () => {
      // Arrange
      const originalTimeout = process.env.API_TIMEOUT;
      delete process.env.API_TIMEOUT;

      // Act
      const service = new UserService();

      // Assert
      expect(service.config.apiTimeout).toBe(5000); // Default value

      // Cleanup
      if (originalTimeout) {
        process.env.API_TIMEOUT = originalTimeout;
      }
    });
  });
});

// Integration tests example
describe('UserService Integration Tests', () => {
  let userService: UserService;

  beforeAll(async () => {
    // Setup test database
    await setupTestDatabase();
    server.listen();
  });

  afterAll(async () => {
    await teardownTestDatabase();
    server.close();
  });

  beforeEach(async () => {
    await clearTestDatabase();
    userService = new UserService(); // Use real dependencies for integration tests
  });

  it('should create user end-to-end with real database', async () => {
    // This test uses a real test database but mocked external services
    const createUserRequest = UserFactory.createUserRequest({
      email: '<EMAIL>'
    });

    const result = await userService.createUser(createUserRequest);

    expect(result.success).toBe(true);

    // Verify in database
    const userInDb = await userService.getUserById(result.data.id);
    expect(userInDb.success).toBe(true);
    expect(userInDb.data.email).toBe('<EMAIL>');
  });
});

// Helper functions for test database management
async function setupTestDatabase() {
  // Setup test database schema
}

async function teardownTestDatabase() {
  // Cleanup test database
}

async function clearTestDatabase() {
  // Clear all data between tests
}