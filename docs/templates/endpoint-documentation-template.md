# [HTTP Method] /api/[resource]/[endpoint]

## Overview

**Purpose**: [Brief description of what this endpoint does]

**Method**: `[GET|POST|PUT|DELETE|PATCH]`

**URL**: `/api/[resource]/[endpoint]`

**Authentication**: [Required|Optional|None] - [JWT Bearer <PERSON>|API Key|etc.]

**Rate Limiting**: [X requests per minute/hour] (if applicable)

---

## Request

### URL Parameters

| Parameter | Type          | Required | Description       | Example                                |
| --------- | ------------- | -------- | ----------------- | -------------------------------------- |
| `id`      | string (UUID) | Yes      | Unique identifier | `123e4567-e89b-12d3-a456-************` |

### Query Parameters

| Parameter   | Type    | Required | Default     | Description                   | Example      |
| ----------- | ------- | -------- | ----------- | ----------------------------- | ------------ |
| `page`      | integer | No       | 1           | Page number for pagination    | `2`          |
| `limit`     | integer | No       | 20          | Items per page (max 100)      | `50`         |
| `search`    | string  | No       | -           | Search term                   | `"john doe"` |
| `sortBy`    | string  | No       | `createdAt` | Field to sort by              | `name`       |
| `sortOrder` | string  | No       | `desc`      | Sort direction (`asc`/`desc`) | `asc`        |

### Request Headers

```http
Content-Type: application/json
Authorization: Bearer [JWT_TOKEN]
X-API-Version: v1
```

### Request Body

```typescript
interface [RequestInterface] {
  field1: string;           // Required: Description and constraints
  field2?: number;          // Optional: Description and constraints
  field3: 'enum1' | 'enum2'; // Required: Description of enum values
}
```

#### Example Request

```json
{
  "field1": "example value",
  "field2": 42,
  "field3": "enum1"
}
```

#### Validation Rules

- **field1**: Required, 1-100 characters, alphanumeric only
- **field2**: Optional, positive integer, max 1000
- **field3**: Required, must be one of: `enum1`, `enum2`

---

## Response

### Success Response (200/201)

```typescript
interface [ResponseInterface] {
  success: true;
  data: [DataInterface];
  meta?: {
    timestamp: string;      // ISO 8601 timestamp
    requestId: string;      // Unique request identifier
    // ... pagination if applicable
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}
```

#### Example Success Response

```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "field1": "example value",
    "field2": 42,
    "field3": "enum1",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123456789"
  }
}
```

### Error Responses

#### 400 Bad Request - Validation Errors

```json
{
  "success": false,
  "message": "Validation failed",
  "code": "VALIDATION_ERROR",
  "errors": [
    {
      "field": "field1",
      "message": "Field1 is required",
      "code": "REQUIRED_FIELD"
    }
  ],
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123456789"
  }
}
```

#### 401 Unauthorized

```json
{
  "success": false,
  "message": "Invalid or missing authentication token",
  "code": "UNAUTHORIZED"
}
```

#### 403 Forbidden

```json
{
  "success": false,
  "message": "Insufficient permissions to access this resource",
  "code": "FORBIDDEN"
}
```

#### 404 Not Found

```json
{
  "success": false,
  "message": "Resource not found",
  "code": "NOT_FOUND"
}
```

#### 409 Conflict

```json
{
  "success": false,
  "message": "Resource already exists",
  "code": "CONFLICT"
}
```

#### 422 Unprocessable Entity

```json
{
  "success": false,
  "message": "Business logic validation failed",
  "code": "BUSINESS_LOGIC_ERROR",
  "errors": [
    {
      "field": "status",
      "message": "Cannot change status from active to deleted directly",
      "code": "INVALID_STATUS_TRANSITION"
    }
  ]
}
```

#### 429 Too Many Requests

```json
{
  "success": false,
  "message": "Rate limit exceeded. Try again in 60 seconds",
  "code": "RATE_LIMIT_EXCEEDED"
}
```

#### 500 Internal Server Error

```json
{
  "success": false,
  "message": "An unexpected error occurred",
  "code": "INTERNAL_SERVER_ERROR"
}
```

---

## Frontend Integration

### TypeScript Interface

```typescript
// Import from contracts
import {
  [RequestInterface],
  [ResponseInterface],
  ApiError
} from '../contracts/[ServiceName]';

// API function
export async function [endpointFunction](
  data: [RequestInterface]
): Promise<[ResponseInterface]> {
  const response = await fetch('/api/[resource]/[endpoint]', {
    method: '[METHOD]',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error: ApiError = await response.json();
    throw new APIError(error.message, error.code, error.errors);
  }

  return response.json();
}
```

### React Hook Example

```typescript
import { useState } from 'react';
import { [endpointFunction] } from '../api/[service]';

export function use[EndpointName]() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<[ResponseData] | null>(null);

  const execute = async (requestData: [RequestInterface]) => {
    try {
      setLoading(true);
      setError(null);

      const result = await [endpointFunction](requestData);
      setData(result.data);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    execute,
    loading,
    error,
    data,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
}
```

### Usage in Component

```typescript
function ExampleComponent() {
  const { execute, loading, error, data } = use[EndpointName]();

  const handleSubmit = async (formData: [RequestInterface]) => {
    try {
      await execute(formData);
      // Handle success
      console.log("Success:", data);
    } catch (error) {
      // Handle error
      console.error("Error:", error);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return <div>{/* Your component JSX */}</div>;
}
```

### Error Handling Example

```typescript
import { APIError } from "../utils/errors";

try {
  const result = await [endpointFunction](data);
  // Handle success
} catch (error) {
  if (error instanceof APIError) {
    // Handle API-specific errors
    switch (error.code) {
      case "VALIDATION_ERROR":
        // Show validation errors
        error.validationErrors?.forEach((validationError) => {
          setFieldError(validationError.field, validationError.message);
        });
        break;
      case "UNAUTHORIZED":
        // Redirect to login
        redirectToLogin();
        break;
      case "FORBIDDEN":
        // Show permission error
        showPermissionError();
        break;
      default:
        // Show generic error
        showGenericError(error.message);
    }
  } else {
    // Handle network or other errors
    showNetworkError();
  }
}
```

---

## Testing

### Unit Test Example

```typescript
import { [endpointFunction] } from '../[service]';
import { setupServer } from 'msw/node';
import { rest } from 'msw';

const server = setupServer(
  rest.[method]('/api/[resource]/[endpoint]', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          // Mock response data
        }
      })
    );
  })
);

describe('[endpointFunction]', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('should successfully [describe action]', async () => {
    const requestData = {
      // Test data
    };

    const result = await [endpointFunction](requestData);

    expect(result.success).toBe(true);
    expect(result.data).toMatchObject({
      // Expected response structure
    });
  });

  it('should handle validation errors', async () => {
    server.use(
      rest.[method]('/api/[resource]/[endpoint]', (req, res, ctx) => {
        return res(
          ctx.status(400),
          ctx.json({
            success: false,
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
            errors: [
              {
                field: 'field1',
                message: 'Field1 is required',
                code: 'REQUIRED_FIELD'
              }
            ]
          })
        );
      })
    );

    await expect([endpointFunction]({})).rejects.toThrow('Validation failed');
  });
});
```

---

## Performance Considerations

- **Response Time**: Expected response time under normal load: [X]ms
- **Rate Limiting**: [X] requests per minute per user
- **Payload Size**: Maximum request/response size: [X]KB
- **Database Impact**: [Number] of database queries per request
- **Caching**: [Describe caching strategy if applicable]

## Business Logic

- **Purpose**: [Detailed explanation of business purpose]
- **Rules**: [Any business rules or constraints]
- **Side Effects**: [Any side effects this endpoint triggers]
- **Dependencies**: [Other services or systems this depends on]

## Changelog

| Version | Date       | Changes                  |
| ------- | ---------- | ------------------------ |
| 1.0.0   | 2024-01-15 | Initial implementation   |
| 1.1.0   | 2024-01-20 | Added pagination support |

## Related Endpoints

- `GET /api/[resource]` - List all [resources]
- `POST /api/[resource]` - Create new [resource]
- `PUT /api/[resource]/{id}` - Update [resource]
- `DELETE /api/[resource]/{id}` - Delete [resource]

## Security Considerations

- **Authentication**: [Details about auth requirements]
- **Authorization**: [Permission levels required]
- **Data Validation**: [Input sanitization and validation]
- **Rate Limiting**: [Protection against abuse]
- **CORS**: [Cross-origin request policies]
