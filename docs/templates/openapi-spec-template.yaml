# docs/templates/openapi-spec-template.yaml

openapi: 3.0.3
info:
  title: [Service Name] API
  description: |
    [Detailed description of what this API service does]
    
    ## Authentication
    [Describe authentication method - JWT, API Key, etc.]
    
    ## Rate Limiting
    [Describe rate limiting policies if applicable]
    
    ## Error Handling
    All errors follow a consistent format with appropriate HTTP status codes.
  version: 1.0.0
  contact:
    name: [Team Name]
    email: [<EMAIL>]
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.production.com/v1
    description: Production server
  - url: https://api.staging.com/v1
    description: Staging server
  - url: http://localhost:3000/api/v1
    description: Local development server

# Security schemes
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/login endpoint
    
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for service-to-service communication

  # Reusable schemas
  schemas:
    # Error response schema
    ApiError:
      type: object
      required:
        - success
        - message
        - code
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        code:
          type: string
          example: "VALIDATION_ERROR"
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
              example: "2024-01-15T10:30:00Z"
            requestId:
              type: string
              example: "req_123456789"

    ValidationError:
      type: object
      required:
        - field
        - message
        - code
      properties:
        field:
          type: string
          example: "email"
        message:
          type: string
          example: "Email is required"
        code:
          type: string
          example: "REQUIRED_FIELD"

    # Success response wrapper
    SuccessResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          description: "The actual response data"
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
              example: "2024-01-15T10:30:00Z"
            requestId:
              type: string
              example: "req_123456789"

    # Pagination schema
    PaginationMeta:
      type: object
      required:
        - page
        - limit
        - total
        - totalPages
      properties:
        page:
          type: integer
          minimum: 1
          example: 1
        limit:
          type: integer
          minimum: 1
          maximum: 100
          example: 20
        total:
          type: integer
          minimum: 0
          example: 150
        totalPages:
          type: integer
          minimum: 0
          example: 8

    # Example entity schema template
    ExampleEntity:
      type: object
      required:
        - id
        - name
        - createdAt
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
          description: "Unique identifier for the entity"
        name:
          type: string
          minLength: 1
          maxLength: 100
          example: "Example Name"
          description: "Display name of the entity"
        description:
          type: string
          maxLength: 500
          example: "This is an example description"
          description: "Optional description of the entity"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"
          description: "Current status of the entity"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
          description: "When the entity was created"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
          description: "When the entity was last updated"

    # Request schemas
    CreateExampleRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          example: "New Example"
        description:
          type: string
          maxLength: 500
          example: "Description for the new example"

    UpdateExampleRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          example: "Updated Example"
        description:
          type: string
          maxLength: 500
          example: "Updated description"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "inactive"

# Global security requirement (can be overridden per endpoint)
security:
  - bearerAuth: []

# API paths
paths:
  # Example CRUD endpoints
  /examples:
    get:
      tags: [Examples]
      summary: List examples
      description: |
        Retrieve a paginated list of examples with optional filtering and sorting.
        
        ## Filtering
        - Use query parameters to filter results
        - Multiple filters are combined with AND logic
        
        ## Sorting
        - Default sort is by createdAt descending
        - Use sortBy and sortOrder parameters to customize
      operationId: getExamples
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          description: Search term to filter by name or description
          required: false
          schema:
            type: string
            maxLength: 100
        - name: status
          in: query
          description: Filter by status
          required: false
          schema:
            type: string
            enum: [active, inactive, suspended]
        - name: sortBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [name, createdAt, updatedAt]
            default: createdAt
        - name: sortOrder
          in: query
          description: Sort order
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Successfully retrieved examples
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ExampleEntity'
                      meta:
                        allOf:
                          - $ref: '#/components/schemas/PaginationMeta'
                          - type: object
                            properties:
                              timestamp:
                                type: string
                                format: date-time
              examples:
                successful_response:
                  summary: Successful response with examples
                  value:
                    success: true
                    data:
                      - id: "123e4567-e89b-12d3-a456-************"
                        name: "Example 1"
                        description: "First example"
                        status: "active"
                        createdAt: "2024-01-15T10:30:00Z"
                        updatedAt: "2024-01-15T10:30:00Z"
                      - id: "223e4567-e89b-12d3-a456-426614174001"
                        name: "Example 2"
                        description: "Second example"
                        status: "active"
                        createdAt: "2024-01-14T09:15:00Z"
                        updatedAt: "2024-01-14T09:15:00Z"
                    meta:
                      page: 1
                      limit: 20
                      total: 2
                      totalPages: 1
                      timestamp: "2024-01-15T10:30:00Z"
                      requestId: "req_123456789"
        '400':
          description: Bad request - invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
              examples:
                invalid_parameters:
                  summary: Invalid query parameters
                  value:
                    success: false
                    message: "Invalid query parameters"
                    code: "INVALID_PARAMETERS"
                    errors:
                      - field: "limit"
                        message: "Limit must be between 1 and 100"
                        code: "INVALID_RANGE"
        '401':
          description: Unauthorized - missing or invalid authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    post:
      tags: [Examples]
      summary: Create example
      description: Create a new example with the provided data
      operationId: createExample
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExampleRequest'
            examples:
              create_example:
                summary: Create a new example
                value:
                  name: "New Example"
                  description: "This is a new example"
      responses:
        '201':
          description: Successfully created example
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ExampleEntity'
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '409':
          description: Conflict - example with same name already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /examples/{id}:
    get:
      tags: [Examples]
      summary: Get example by ID
      description: Retrieve a specific example by its unique identifier
      operationId: getExampleById
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the example
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Successfully retrieved example
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ExampleEntity'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '404':
          description: Example not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    put:
      tags: [Examples]
      summary: Update example
      description: Update an existing example with new data
      operationId: updateExample
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the example
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateExampleRequest'
      responses:
        '200':
          description: Successfully updated example
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ExampleEntity'
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '404':
          description: Example not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    delete:
      tags: [Examples]
      summary: Delete example
      description: Delete an existing example
      operationId: deleteExample
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the example
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Successfully deleted example
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          message:
                            type: string
                            example: "Example deleted successfully"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '404':
          description: Example not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

# Tags for grouping endpoints
tags:
  - name: Examples
    description: Operations related to example entities
  - name: Authentication
    description: User authentication and authorization
  - name: Health
    description: API health and status endpoints