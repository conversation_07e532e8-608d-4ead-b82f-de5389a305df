# Frontend Integration Guide for New API Endpoints

This guide shows how to integrate the new endpoints with TypeScript types, fetch/axios examples, and state considerations.

## League subscription requirement

PATCH /api/v1/leagues/:id/subscription_requirement (owner only)

Types:
```ts
export interface PatchLeagueSubscriptionRequirementRequest { require_subscription: boolean }
export interface LeagueResponse {
  id: number; name: string; subscriber_only: boolean; youtube_channel_id: string | null; owner_id: number;
}
```

fetch example:
```ts
const res = await fetch(`${API_URL}/api/v1/leagues/${leagueId}/subscription_requirement`, {
  method: 'PATCH',
  headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
  body: JSON.stringify({ require_subscription })
});
if (!res.ok) throw await res.json();
const league: LeagueResponse = await res.json();
```

State considerations:
- Toggle UI should be disabled unless user is owner
- On enable failures, display server message (eligibility reason)

## Admin Users

Routes under /api/v1/admin/users (admin only).

Types:
```ts
export interface AdminUser { id: number; email: string; username: string; admin: boolean; created_at?: string; updated_at?: string; youtube_channel_id?: string|null; youtube_channel_name?: string|null; is_content_creator?: boolean; youtube_subscriber_count?: number|null }
export interface AdminUsersIndexResponse { data: AdminUser[]; pagination: { page?: number; per_page?: number; total_pages?: number; total_count?: number } }
```

fetch example (index):
```ts
const res = await fetch(`${API_URL}/api/v1/admin/users?email=${encodeURIComponent(q)}`, { headers: { Authorization: `Bearer ${adminToken}` } });
if (!res.ok) throw await res.json();
const payload: AdminUsersIndexResponse = await res.json();
```

State considerations:
- Keep filters in URL/query state
- Handle pagination via server metadata when available

## Round scoring

Three GET routes. Requires auth.

Types:
```ts
export interface RoundScoreBreakdown { match_id: number; points: 0|1|3|null }
export interface LeagueRoundScoresResponse { league_id: number; round_number: number; scores: { user_id: number; username: string; round_number: number; total_points: number; breakdown: RoundScoreBreakdown[] }[] }
export interface LeagueRoundWinnersResponse { league_id: number; round_number: number; winners: { rank: number; user_id: number; username: string; total_points: number }[] }
export interface UserRoundScoreResponse { user_id: number; username: string; round_number: number; total_points: number; breakdown: RoundScoreBreakdown[] }
```

Example:
```ts
const res = await fetch(`${API_URL}/api/v1/leagues/${leagueId}/rounds/${round}/scores`, { headers: { Authorization: `Bearer ${token}` }});
if (!res.ok) throw await res.json();
const payload: LeagueRoundScoresResponse = await res.json();
```

State considerations:
- For completed rounds, cache client-side or via SWR/RTK Query
- Winners can be derived from scores but endpoint provides top 3 directly

## OAuth scope reduction

PATCH /api/v1/youtube_auth/reduce_scope

```ts
export type ReduceScopeRequest = { scopes: ['openid','email','profile'] }
export type ReduceScopeResponse = { success: true; scopes: ['openid','email','profile'] }
```

Example:
```ts
const res = await fetch(`${API_URL}/api/v1/youtube_auth/reduce_scope`, {
  method: 'PATCH',
  headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
  body: JSON.stringify({ scopes: ['openid','email','profile'] })
});
if (!res.ok) throw await res.json();
```

UX:
- Make clear that reduction is one-way; to re-expand use the upgrade flow

