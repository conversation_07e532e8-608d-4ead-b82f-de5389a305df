# Frontend AI Prompt: YouTube OAuth Connect Fix

## Issue Fixed
The backend YouTube OAuth connect endpoint `/api/v1/youtube_auth/connect` was returning a 404 error because the `connect` method was incorrectly placed in the private section of the controller. This has been fixed and the endpoint is now accessible.

## What the Frontend Needs to Do

### 1. Verify Current Implementation
Check your current YouTube OAuth integration code to ensure it's calling the correct endpoint:
- **Endpoint**: `POST /api/v1/youtube_auth/connect`
- **Authentication**: Required (Bearer token in Authorization header)

### 2. Request Format
Ensure your request includes the required parameters for the OAuth flow:

```javascript
const connectYouTubeAccount = async (authCode, redirectUri, clientId) => {
  const response = await fetch('/api/v1/youtube_auth/connect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${userToken}`
    },
    body: JSON.stringify({
      code: authCode,
      redirect_uri: redirectUri,
      client_id: clientId
    })
  });
  
  return response.json();
};
```

### 3. Error Handling
Update your error handling to cover all possible error responses:

```javascript
const handleConnectResponse = async (response) => {
  if (response.ok) {
    const data = await response.json();
    // Success: data.message, data.user
    return { success: true, data };
  }
  
  const errorData = await response.json();
  
  switch (response.status) {
    case 400:
      // Missing parameters, invalid OAuth config, token exchange failed, or no YouTube channel
      return { success: false, error: errorData.error, details: errorData.details };
    
    case 403:
      // Feature disabled
      return { success: false, error: 'YouTube Connect is currently unavailable' };
    
    case 409:
      // Channel already connected to another account
      return { success: false, error: 'This YouTube channel is already connected to another account' };
    
    case 500:
      // Server error
      return { success: false, error: 'An unexpected error occurred. Please try again.' };
    
    default:
      return { success: false, error: 'An unknown error occurred' };
  }
};
```

### 4. User Experience Improvements
Consider implementing these UX improvements:

1. **Loading States**: Show loading indicators during the OAuth process
2. **Clear Error Messages**: Display user-friendly error messages for each error type
3. **Retry Logic**: Allow users to retry the connection process if it fails
4. **Status Checking**: Use the `/api/v1/youtube_auth/status` endpoint to check connection status

### 5. Testing
Test the following scenarios:
1. Successful YouTube account connection
2. Missing or invalid OAuth parameters
3. YouTube channel already connected to another account
4. User without a YouTube channel
5. Network/server errors

### 6. Environment Configuration
Ensure your frontend has the correct Google OAuth configuration:
- Google Client ID matches the backend configuration
- Redirect URI matches what's configured in the backend
- OAuth scopes include YouTube access permissions

## Expected Behavior After Fix
- The 404 error should no longer occur when calling `/api/v1/youtube_auth/connect`
- Users should be able to successfully connect their YouTube accounts
- Proper error messages should be displayed for various failure scenarios
- The YouTube connection status should be properly reflected in the UI

## Additional Notes
- The backend now includes comprehensive logging for debugging OAuth issues
- All YouTube OAuth endpoints require authentication except for login/signup flows
- The feature can be disabled via environment variables, so handle 403 responses appropriately

## Documentation Reference
See `docs/api/youtube-oauth-connect.md` for complete API documentation including all request/response formats and error codes.
