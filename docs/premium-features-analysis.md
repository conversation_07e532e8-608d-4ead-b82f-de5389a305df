# Premium Features Analysis for Brag Rights

## Executive Summary

Brag Rights is well-positioned to introduce a premium tier leveraging existing capabilities: YouTube subscriber-only leagues, a rich statistics engine (basic/advanced/competition/comparative), archival/season transitions, league standings, and robust prediction flows. Near‑term monetization can focus on gating advanced analytics and enhanced league management for creators, while planning medium‑term features like custom scoring systems, data export, and API access. This document maps the current backend/front‑end capabilities to premium opportunities, estimates effort, and recommends a phased rollout.

## Current Premium Features (Implemented)

- YouTube integration
  - Subscriber‑only leagues and creator mode
    - Model support: `League` has `youtube_league`, `youtube_channel_id`, `subscriber_only`, `subscriber_requirement_type`, `min_subscriber_date`, `unsubscribe_policy` (see db/schema.rb, app/models/league.rb)
    - Creator eligibility: `YoutubeEligibilityService` (checks connected YouTube, scope, creator status, verified channel, MIN_SUBSCRIBERS_FOR_LEAGUE)
    - Subscription verification: `BragRightsYouTubeService#verify_subscription?`, cached + quota‑aware
    - Endpoints: `/api/v1/youtube_auth/*` (connect, status, verify_subscription, scope control), `/api/v1/youtube/*` (leagues, creator status, eligibility)
    - Admin: `/api/v1/admin/youtube/quota_usage`
  - Frontend: creator toggle, subscriber‑only league guard, subscription verification UI
- League and Season management
  - League creation/update, archiving/unarchiving, join requests, memberships
  - Season transition and archive services (`LeagueArchiveService`, `LeagueSeasonTransitionService`)
- Predictions and standings
  - Predictable matches endpoint with season‑over handling
  - League and competition standings endpoints (current and historical by season)
- Statistics engine
  - `UserStatisticsService` supports basic, advanced (consistency, streaks), competition, comparative, and combined (all) stats via `/api/v1/users/:user_id/statistics`

## Existing Features to Monetize

- Advanced analytics and insights (Low–Medium effort)
  - Gate `advanced`, `competition`, `comparative`, and `all` stats under premium while keeping `basic` free
  - Add parameters/feature flags server‑side in `UserStatisticsController` for access control
  - Extend caching and background computation for premium users (priority or deeper retention)
- Historical standings and league history (Low effort)
  - Leverage existing `league.calculate_standings_for_season` and archived seasons; add premium filter UI and endpoint responses (already partially present)
- Creator tools around subscriber‑only leagues (Low–Medium effort)
  - Gate subscriber‑only league creation to premium creators (alongside YouTube minimums)
  - Premium moderation tools (bulk approvals, invite links, audit logs)
- Favorite‑team and competition leaderboards (Low effort)
  - Expand competition‑wide standings already implemented to premium placement and filters (e.g., by season, timeframe)

## Proposed New Premium Features (with estimates)

- Advanced Prediction Tools (AI suggestions, trend insights) — High effort
  - ML‑assisted suggestions using historical performance, team form, consensus vs. contrarian deltas
  - Backend service to compute features and serve suggestions; careful with costs and explainability
  - Effort: BE 10–15 pts, FE 8–12 pts, Ops 3–5 pts
- Enhanced League Management — Medium effort
  - Custom scoring systems per league (see docs/multiple-scoring-systems-analysis.md)
  - Member caps, invite‑only codes, advanced moderation workflows, scheduled lock times
  - Effort: BE 8–12 pts, FE 6–8 pts
- User Profile Customization and Achievements — Low–Medium effort
  - Badges for milestones (perfect rounds, streaks, percentiles), profile themes
  - Effort: BE 3–6 pts, FE 4–6 pts
- Priority Features and Support — Low effort
  - Early access competitions, faster stats refresh, priority support queue
  - Effort: BE 2–4 pts, Ops/Support 2–4 pts
- Data Export/Import — Medium effort
  - CSV/JSON export of user predictions, league standings; import league invites
  - Effort: BE 5–8 pts, FE 3–5 pts
- API Access for Integrations — Medium–High effort
  - Personal API tokens and scoped endpoints (read‑only to start) for leagues, standings, and user stats
  - Rate limiting, docs, and token management
  - Effort: BE 8–12 pts, FE 4–6 pts, DevRel/docs 3–5 pts
- Creator‑centric YouTube Features — Medium effort
  - Embed‑ready widgets (live table, round winners), automatic subscriber sync, channel‑branded league pages
  - Effort: BE 5–8 pts, FE 6–8 pts

## Comparison with Pricing Strategy Documents

- docs/pricing-strategies/copilot-pricing-strategy.txt
  - Updated growth/cost scenarios (conservative vs. aggressive), FE costs included, CPM assumptions conservative
  - Recommendation aligns: start conservative, push aggressive during major tournaments
- PDFs (Pricing strategy - V3.pdf, Pricing strategy - multiple estimates.pdf)
  - Not parsed here, but presumed to include tiers and revenue assumptions; premium gating of analytics and creator tools fits both conservative and aggressive scenarios

Key takeaways for premium packaging:
- Tier 1 (Starter/Free): basic predictions, basic stats, public leagues
- Tier 2 (Premium): advanced stats (all modes), historical standings, data export, early access competitions
- Tier 3 (Creator): everything in Premium + subscriber‑only leagues, custom scoring, moderation toolkit, API access

## Technical Implementation Considerations

- Access control
  - Add a premium/plan attribute on User or a separate Subscription model; integrate checks in controllers/services (e.g., UserStatisticsController, LeaguesController)
  - Introduce feature flags for gradual rollout
- Performance and caching
  - Extend caching keys in UserStatisticsController per plan; precompute heavy stats for premium
  - Background jobs for recomputation; ensure quota management for YouTube APIs
- Data model extensions
  - Scoring: add `scoring_rule_types` and `league_scoring_rules` (see multiple-scoring-systems-analysis.md)
  - Subscriptions: lightweight `subscriptions` table or external billing integration metadata
- API and documentation
  - Document gated endpoints in docs/api_endpoints and keep OpenAPI up to date
  - Add rate limits and response headers for API access tiering
- Security and abuse prevention
  - For API keys: hashed token storage, per‑key rate limits, scopes; avoid elevated scopes for YouTube (already respected)

## Revenue Potential Assessment (qualitative)

- Advanced analytics: High engagement, medium ARPU; strong upgrade lever for competitive users
- Creator tools + subscriber‑only leagues: High value for influencers; lower volume but higher ARPU
- API access: Niche but sticky for communities and 3rd‑party tools
- Data export/import: Support/retention feature; modest direct revenue impact
- AI suggestions: Potentially strong differentiator; ensure cost controls and clear ROI

## Recommended Implementation Priority

1) Short‑term (Low lift, fast ROI)
- Gate advanced stats and historical standings under Premium
- Add creator moderation tools and UI for subscriber‑only leagues
- Add documentation updates and pricing copy; enforce MIN_SUBSCRIBERS_FOR_LEAGUE consistently (already centralized)

2) Mid‑term (Meaningful differentiation)
- Custom scoring systems per league
- Data export for predictions and standings
- Creator widgets and branded pages

3) Longer‑term (Strategic)
- API access with tokens and scopes
- AI‑assisted prediction suggestions

## Mapping to Current Code

- Stats engine: `app/services/user_statistics_service.rb` and `/api/v1/users/:id/statistics` already support multiple modes (basic, advanced, competition, comparative, all);
  add plan checks and incremental caching.
- Creator gating: `YoutubeEligibilityService`, `LeaguesController#subscription_requirement`, and `League#user_meets_subscription_requirements?` are the enforcement points; extend to plan‑based gating.
- Standings/history: `LeaguesController#standings` and `CompetitionsController#standings` support season context; add premium filters/UI.
- Jobs & caching: `sidekiq` scheduler in place; extend background precomputation for premium cohorts.

