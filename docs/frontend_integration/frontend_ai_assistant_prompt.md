# Frontend AI Assistant Prompt for YouTube OAuth Integration

## Context

You are a frontend development AI assistant helping to implement YouTube OAuth integration for a sports prediction platform called "Brag Rights". The backend has been fully implemented and validated with comprehensive contract testing.

## System Overview

### Backend Implementation Status ✅
- **Progressive OAuth Flow**: Fully implemented and tested
- **Content Creator Verification**: 100+ subscriber requirement validated
- **Subscriber-Only Leagues**: Complete business logic implemented
- **API Contracts**: All endpoints validated with comprehensive test suite
- **Security**: OAuth state validation, token management, scope verification

### Technology Stack
- **Backend**: Rails 7 API with comprehensive YouTube OAuth integration
- **Frontend**: Next.js (assumed) - adapt as needed for your framework
- **Authentication**: Bearer token-based with progressive OAuth scopes
- **API Base URL**: `http://localhost:3000/api/v1` (development)

## API Endpoints (Fully Implemented & Tested)

### YouTube Auth Endpoints
1. `GET /youtube_auth/status` - Get OAuth status and scope information
2. `GET /youtube_auth/scope_upgrade_url` - Generate OAuth URL for scope upgrade
3. `POST /youtube_auth/upgrade_scope` - Complete OAuth scope upgrade
4. `POST /youtube_auth/verify_subscription` - Verify channel subscription

### YouTube Controller Endpoints
1. `GET /youtube/subscription_status` - Check subscription to specific channel
2. `POST /youtube/update_creator_status` - Enable/disable content creator mode
3. `GET /youtube/check_subscriber_league_eligibility` - Check league creation eligibility

## Progressive OAuth Flow (Backend Validated)

### Phase 1: Basic Authentication
- Users sign up with minimal OAuth scope: `openid email profile`
- No YouTube permissions initially required
- Users can use basic app features

### Phase 2: YouTube Scope Upgrade
- When YouTube features needed, prompt for scope upgrade
- Additional scope: `https://www.googleapis.com/auth/youtube.readonly`
- Seamless upgrade flow without re-authentication

### Phase 3: Content Creator Features
- Users with 100+ subscribers can enable creator mode
- Can create subscriber-only leagues
- Full YouTube API integration available

## Implementation Requirements

### 1. State Management
```javascript
// Required state structure (adapt to your state management solution)
const youtubeState = {
  // From /youtube_auth/status endpoint
  connected: boolean,
  is_content_creator: boolean,
  channel_id: string|null,
  channel_name: string|null,
  subscriber_count: number|null,
  has_basic_scope: boolean,
  has_youtube_scope: boolean,
  needs_scope_upgrade: boolean,
  can_use_youtube_features: boolean,
  
  // UI state
  loading: boolean,
  error: string|null,
  upgrading_scope: boolean
};
```

### 2. Core Components Needed
1. **YouTubeStatusIndicator** - Show connection status
2. **ScopeUpgradePrompt** - Guide users through scope upgrade
3. **ContentCreatorVerification** - Enable creator mode
4. **SubscriberLeagueEligibility** - Check league creation eligibility
5. **OAuthCallbackHandler** - Handle OAuth redirects

### 3. Error Handling Patterns
```javascript
// Handle scope upgrade requirements
if (error.needs_scope_upgrade || error.message?.includes('scope upgrade')) {
  // Show scope upgrade prompt instead of generic error
  showScopeUpgradePrompt();
} else {
  // Handle other errors normally
  showErrorMessage(error.message);
}
```

### 4. User Journey Flows

#### New User Flow
1. User signs up with basic OAuth
2. User explores app with basic features
3. User attempts YouTube feature → scope upgrade prompt
4. User completes scope upgrade → full features available

#### Content Creator Flow
1. User connects YouTube account (scope upgrade if needed)
2. System verifies 100+ subscribers
3. User enables content creator mode
4. User can create subscriber-only leagues

#### League Participation Flow
1. User finds subscriber-only league
2. System checks if user is subscribed to creator's channel
3. If not subscribed → show subscription requirement
4. If subscribed → allow league joining

## API Integration Examples

### Check YouTube Status
```javascript
const response = await fetch('/api/v1/youtube_auth/status', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const status = await response.json();

// Use status.needs_scope_upgrade to show upgrade prompt
// Use status.can_use_youtube_features to enable/disable features
```

### Initiate Scope Upgrade
```javascript
const response = await fetch(`/api/v1/youtube_auth/scope_upgrade_url?redirect_uri=${redirectUri}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
const { oauth_url, state } = await response.json();

// Store state for validation
localStorage.setItem('oauth_state', state);
// Redirect to OAuth URL
window.location.href = oauth_url;
```

### Complete Scope Upgrade
```javascript
// In OAuth callback handler
const response = await fetch('/api/v1/youtube_auth/upgrade_scope', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ code, state, redirect_uri })
});
```

## Security Considerations

### OAuth State Validation
- Always validate state parameter in OAuth callbacks
- Store state securely (localStorage for SPA, session for SSR)
- Clear state after successful validation

### Token Management
- Never store tokens in localStorage in production
- Use secure HTTP-only cookies or secure session storage
- Implement token refresh logic

### Error Handling
- Don't expose sensitive error details to users
- Log detailed errors for debugging
- Provide user-friendly error messages

## Testing Strategy

### Unit Tests
- Mock all API responses
- Test error handling scenarios
- Verify state management updates

### Integration Tests
- Test complete OAuth flows
- Verify scope upgrade functionality
- Test content creator verification

### User Acceptance Tests
- Test user journeys end-to-end
- Verify error messages are user-friendly
- Test on different devices/browsers

## Common Implementation Patterns

### Conditional Feature Rendering
```javascript
// Show features based on OAuth scope
{status.can_use_youtube_features ? (
  <YouTubeFeatures />
) : status.needs_scope_upgrade ? (
  <ScopeUpgradePrompt />
) : (
  <ConnectYouTubePrompt />
)}
```

### Progressive Enhancement
```javascript
// Gracefully handle missing YouTube features
const handleCreateLeague = () => {
  if (!status.can_use_youtube_features) {
    showScopeUpgradePrompt();
    return;
  }
  
  if (leagueType === 'subscriber-only' && !status.is_content_creator) {
    showContentCreatorPrompt();
    return;
  }
  
  // Proceed with league creation
  createLeague();
};
```

## Business Logic Validation

### Content Creator Requirements
- Must have YouTube account connected
- Must have YouTube OAuth scope
- Must have 100+ subscribers
- Must enable content creator mode

### Subscriber League Access
- User must be subscribed to creator's channel
- Subscription verified via YouTube API
- Real-time subscription checking

## Performance Considerations

### API Call Optimization
- Cache YouTube status data
- Implement proper loading states
- Use optimistic updates where appropriate

### User Experience
- Show loading indicators during OAuth flows
- Provide clear progress feedback
- Handle network failures gracefully

## Deployment Notes

### Environment Variables
```
NEXT_PUBLIC_API_BASE_URL=https://api.bragrights.football/api/v1
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_production_client_id
NEXT_PUBLIC_OAUTH_REDIRECT_URI=https://yourdomain.com/auth/callback
```

### Production Considerations
- Use HTTPS for all OAuth redirects
- Implement proper CSP headers
- Monitor OAuth success/failure rates
- Set up error tracking and logging

## Support and Troubleshooting

### Common Issues
1. **OAuth State Mismatch**: Verify state parameter handling
2. **Scope Upgrade Failures**: Check redirect URI configuration
3. **API Authentication**: Verify Bearer token format
4. **CORS Issues**: Ensure proper CORS configuration

### Debug Information
- All API endpoints return detailed error messages
- Backend has comprehensive logging
- Contract tests validate all API responses
- Use browser dev tools to inspect OAuth flows

## Next Steps

1. Implement core state management for YouTube OAuth
2. Create OAuth callback handler component
3. Build scope upgrade user interface
4. Implement content creator verification flow
5. Add subscriber league eligibility checking
6. Test complete user journeys
7. Deploy and monitor OAuth success rates

The backend is fully implemented and tested. Focus on creating a smooth user experience that guides users through the progressive OAuth flow while handling errors gracefully.
