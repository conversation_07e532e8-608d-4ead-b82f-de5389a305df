# YouTube OAuth Frontend Integration Guide

## Overview

This guide provides technical implementation details for integrating the YouTube OAuth progressive authentication system with the frontend application.

## Architecture Overview

The YouTube OAuth system uses a progressive authentication approach:

1. **Basic OAuth**: Users initially authenticate with minimal scope (openid, email, profile)
2. **Progressive Upgrade**: When YouTube features are needed, users upgrade to include YouTube scope
3. **Feature Access**: Full YouTube features available after scope upgrade

## Implementation Steps

### 1. Initial Setup

#### Environment Variables
```javascript
// Required environment variables
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback
```

#### API Client Configuration
```javascript
// api/client.js
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export const apiClient = {
  get: (endpoint, token) => fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }),
  
  post: (endpoint, data, token) => fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
};
```

### 2. YouTube Status Management

#### Check YouTube Connection Status
```javascript
// hooks/useYouTubeStatus.js
import { useState, useEffect } from 'react';
import { apiClient } from '../api/client';

export const useYouTubeStatus = (token) => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/youtube_auth/status', token);
      
      if (!response.ok) {
        throw new Error('Failed to fetch YouTube status');
      }
      
      const data = await response.json();
      setStatus(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchStatus();
    }
  }, [token]);

  return { status, loading, error, refetch: fetchStatus };
};
```

#### YouTube Status Component
```javascript
// components/YouTubeStatus.jsx
import React from 'react';
import { useYouTubeStatus } from '../hooks/useYouTubeStatus';

export const YouTubeStatus = ({ token }) => {
  const { status, loading, error } = useYouTubeStatus(token);

  if (loading) return <div>Loading YouTube status...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!status) return null;

  return (
    <div className="youtube-status">
      <h3>YouTube Connection Status</h3>
      <div>
        <strong>Connected:</strong> {status.connected ? 'Yes' : 'No'}
      </div>
      
      {status.connected && (
        <>
          <div>
            <strong>Channel:</strong> {status.channel_name || 'N/A'}
          </div>
          <div>
            <strong>Subscribers:</strong> {status.subscriber_count || 'N/A'}
          </div>
          <div>
            <strong>Content Creator:</strong> {status.is_content_creator ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>YouTube Features Available:</strong> {status.can_use_youtube_features ? 'Yes' : 'No'}
          </div>
          
          {status.needs_scope_upgrade && (
            <div className="alert alert-warning">
              <strong>Action Required:</strong> YouTube scope upgrade needed for full features
            </div>
          )}
        </>
      )}
    </div>
  );
};
```

### 3. Progressive OAuth Scope Upgrade

#### Scope Upgrade Hook
```javascript
// hooks/useYouTubeScopeUpgrade.js
import { useState } from 'react';
import { apiClient } from '../api/client';

export const useYouTubeScopeUpgrade = (token) => {
  const [upgrading, setUpgrading] = useState(false);
  const [error, setError] = useState(null);

  const initiateUpgrade = async () => {
    try {
      setUpgrading(true);
      setError(null);

      // Get OAuth URL for scope upgrade
      const response = await apiClient.get(
        `/youtube_auth/scope_upgrade_url?redirect_uri=${encodeURIComponent(process.env.NEXT_PUBLIC_OAUTH_REDIRECT_URI)}`,
        token
      );

      if (!response.ok) {
        throw new Error('Failed to get OAuth URL');
      }

      const data = await response.json();
      
      // Store state for validation
      localStorage.setItem('oauth_state', data.state);
      
      // Redirect to OAuth URL
      window.location.href = data.oauth_url;
      
    } catch (err) {
      setError(err.message);
      setUpgrading(false);
    }
  };

  const completeUpgrade = async (code, state) => {
    try {
      setUpgrading(true);
      setError(null);

      // Validate state
      const storedState = localStorage.getItem('oauth_state');
      if (state !== storedState) {
        throw new Error('Invalid state parameter');
      }

      // Complete scope upgrade
      const response = await apiClient.post('/youtube_auth/upgrade_scope', {
        code,
        state,
        redirect_uri: process.env.NEXT_PUBLIC_OAUTH_REDIRECT_URI
      }, token);

      if (!response.ok) {
        throw new Error('Failed to upgrade OAuth scope');
      }

      // Clean up stored state
      localStorage.removeItem('oauth_state');
      
      return await response.json();
      
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setUpgrading(false);
    }
  };

  return { initiateUpgrade, completeUpgrade, upgrading, error };
};
```

#### OAuth Callback Handler
```javascript
// pages/auth/callback.js
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useYouTubeScopeUpgrade } from '../../hooks/useYouTubeScopeUpgrade';

export default function OAuthCallback() {
  const router = useRouter();
  const { completeUpgrade } = useYouTubeScopeUpgrade();
  const [status, setStatus] = useState('processing');

  useEffect(() => {
    const handleCallback = async () => {
      const { code, state, error } = router.query;

      if (error) {
        setStatus('error');
        console.error('OAuth error:', error);
        return;
      }

      if (code && state) {
        try {
          await completeUpgrade(code, state);
          setStatus('success');
          
          // Redirect back to main app
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
          
        } catch (err) {
          setStatus('error');
          console.error('Scope upgrade error:', err);
        }
      }
    };

    if (router.isReady) {
      handleCallback();
    }
  }, [router.isReady, router.query]);

  return (
    <div className="oauth-callback">
      {status === 'processing' && <div>Processing OAuth callback...</div>}
      {status === 'success' && <div>OAuth scope upgraded successfully! Redirecting...</div>}
      {status === 'error' && <div>OAuth upgrade failed. Please try again.</div>}
    </div>
  );
}
```

### 4. Content Creator Features

#### Content Creator Verification
```javascript
// components/ContentCreatorVerification.jsx
import React, { useState } from 'react';
import { apiClient } from '../api/client';

export const ContentCreatorVerification = ({ token, onStatusChange }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const enableCreatorMode = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.post('/youtube/update_creator_status', {}, token);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enable creator mode');
      }

      const data = await response.json();
      onStatusChange?.(data);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="creator-verification">
      <h3>Content Creator Verification</h3>
      <p>Enable content creator mode to create subscriber-only leagues.</p>
      
      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}
      
      <button 
        onClick={enableCreatorMode}
        disabled={loading}
        className="btn btn-primary"
      >
        {loading ? 'Enabling...' : 'Enable Creator Mode'}
      </button>
    </div>
  );
};
```

#### Subscriber League Eligibility Check
```javascript
// hooks/useSubscriberLeagueEligibility.js
import { useState, useEffect } from 'react';
import { apiClient } from '../api/client';

export const useSubscriberLeagueEligibility = (token) => {
  const [eligibility, setEligibility] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkEligibility = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get('/youtube/check_subscriber_league_eligibility', token);
        
        if (!response.ok) {
          throw new Error('Failed to check eligibility');
        }
        
        const data = await response.json();
        setEligibility(data);
        
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      checkEligibility();
    }
  }, [token]);

  return { eligibility, loading, error };
};
```

### 5. Error Handling Patterns

#### Scope Upgrade Error Handler
```javascript
// utils/errorHandlers.js
export const handleScopeUpgradeError = (error, initiateUpgrade) => {
  if (error.message?.includes('scope upgrade') || error.needs_scope_upgrade) {
    return {
      type: 'SCOPE_UPGRADE_REQUIRED',
      message: 'YouTube permissions required for this feature',
      action: initiateUpgrade
    };
  }
  
  return {
    type: 'GENERAL_ERROR',
    message: error.message || 'An error occurred'
  };
};
```

### 6. State Management Integration

#### Redux/Context Integration Example
```javascript
// store/youtubeSlice.js (Redux Toolkit)
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiClient } from '../api/client';

export const fetchYouTubeStatus = createAsyncThunk(
  'youtube/fetchStatus',
  async (token) => {
    const response = await apiClient.get('/youtube_auth/status', token);
    return response.json();
  }
);

const youtubeSlice = createSlice({
  name: 'youtube',
  initialState: {
    status: null,
    loading: false,
    error: null
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchYouTubeStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchYouTubeStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.status = action.payload;
      })
      .addCase(fetchYouTubeStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  }
});

export default youtubeSlice.reducer;
```

## Testing Considerations

### Unit Testing
- Mock API responses for different OAuth states
- Test error handling for scope upgrade scenarios
- Verify state parameter validation

### Integration Testing
- Test complete OAuth flow end-to-end
- Verify progressive scope upgrade functionality
- Test content creator verification process

### Error Scenarios to Test
- Network failures during OAuth flow
- Invalid state parameters
- Expired OAuth tokens
- Insufficient YouTube permissions
