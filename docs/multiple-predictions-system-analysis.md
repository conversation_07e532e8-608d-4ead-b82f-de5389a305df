# Multiple Predictions Per Match System Analysis

## Requirements
- Users can submit different predictions for the same match across different leagues.
- Global prediction is a fallback if no league-specific prediction exists.

## Schema options

### Option A: Separate tables
- global_predictions (id, user_id, match_id, home_score, away_score, created_at)
- league_predictions (id, user_id, league_id, match_id, home_score, away_score, created_at)

Pros:
- Clear isolation; simple constraints
- Fewer nullable fields

Cons:
- Duplication of logic; more joins

### Option B: Single table with nullable league_id
- predictions (id, user_id, match_id, league_id nullable, home_score, away_score, created_at)

Pros:
- Unified logic; simpler fallback query

Cons:
- Need partial unique indexes; careful constraints

## Fallback logic
- For a given user/match in a league: first try league-specific (league_id), else global (NULL league_id).
- Unique indexes:
  - UNIQUE(user_id, match_id, league_id) with NULL league_id allowed once (partial unique index).

## Performance
- Add composite indexes on (user_id, match_id, league_id). Preload in batches.
- Avoid N+1 by fetching both league-specific and globals for a user across matches, then merging in Ruby.

## Data migration
- Convert existing MatchPrediction into predictions with league_id = NULL (global) or map via membership context if needed.
- Backfill unique constraints and deprecate old table.

## Frontend complexity
- UI needs a clear toggle: "Use global prediction" vs "Override for this league".
- Bulk entry forms can provide per-league override rows.

## Implementation effort
- Backend: 10–15 pts (schema, services, migration, endpoints, tests)
- Frontend: 8–12 pts (UI, state, validation)
- Testing: 4–6 pts (unit + integration + data migration)

## Recommendation
Implement later. Useful, but increases complexity across prediction flows and migration risk.

