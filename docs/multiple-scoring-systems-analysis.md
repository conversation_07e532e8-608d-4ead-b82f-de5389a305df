# Multiple Scoring Systems Analysis

## Overview
Introduce per-league scoring rules (e.g., exact=5, correct=2) instead of one global scoring.

## Schema changes
- scoring_rule_types (id, code, name, description) e.g., `classic`, `exact_weighted`, `result_only`.
- league_scoring_rules (id, league_id, scoring_rule_type_id, settings jsonb, created_at)
  - settings example: { exact_points: 5, correct_points: 2, wrong_points: 0 }
- Optional: scoring_snapshots per round to persist calculated results for performance.

Indexes:
- league_scoring_rules.league_id

## Performance impact
- MatchPrediction#calculate_points becomes rule-aware. We should avoid per-record branching by pre-loading the rule and passing it into a vectorized/ruby-level loop.
- For standings, compute totals with eager loading; for heavy leagues, consider background jobs and caching.

## Migration strategy
- Create default `classic` rule for all existing leagues with settings mapping to current 3/1/0.
- Backfill league_scoring_rules for existing leagues.
- Use feature flag to switch endpoints to rule-aware calculators.

## UI/UX complexity
- Creator sets rule type and (optionally) settings when creating a league; for safety, lock rule once predictions start.
- Admin UI to manage default system rule templates.

## Implementation effort (estimates)
- Backend: 8–12 pts (schema, services, serializers, backfill, tests)
- Frontend: 5–8 pts (create/edit forms, display, validation)
- Testing: 3–5 pts (unit + request + migration tests)

## Recommendation
Implement later. Value is moderate but complexity touches core scoring and migration risk. Prioritize YouTube integration stability and admin tooling first.

