// Round scoring contracts
export interface ScoreBreakdownItem {
  match_id: number;
  points: number;
}

export interface LeagueRoundScore {
  user_id: number;
  username: string;
  round_number: number;
  total_points: number;
  breakdown: ScoreBreakdownItem[];
}

export interface LeagueRoundScoresResponse {
  league_id: number;
  round_number: number;
  scores: LeagueRoundScore[];
}

export interface LeagueRoundWinner {
  rank: number; // 1-based rank
  user_id: number;
  username: string;
  total_points: number;
}

export interface LeagueRoundWinnersResponse {
  league_id: number;
  round_number: number;
  winners: LeagueRoundWinner[]; // usually top 3
}

export interface UserRoundScoreResponse {
  user_id: number;
  username: string;
  round_number: number;
  total_points: number;
  breakdown: ScoreBreakdownItem[];
}

export interface ApiError {
  error?: string;
  error_key?: string;
  message?: string;
}

