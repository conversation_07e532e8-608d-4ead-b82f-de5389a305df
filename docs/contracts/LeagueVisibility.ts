// League visibility contracts
// DEPRECATED: The interfaces UpdateLeagueVisibilityRequest/LeagueVisibility/UpdateLeagueVisibilityResponse
// relate to an older toggle-based visibility model. Use ScoringVisibilityMode, RoundVisibility*,
// Showcase*, and UserRoundScore interfaces below instead.
export interface UpdateLeagueVisibilityRequest {
  /** @deprecated */ standings_visible?: boolean;
  /** @deprecated */ round_scores_visible?: boolean;
}

export interface LeagueVisibility {
  id: number;
  standings_visible: boolean;
  round_scores_visible: boolean;
}

export interface UpdateLeagueVisibilityResponse {
  data: LeagueVisibility;
}

export interface ApiError {
  error?: string;
  error_key?: string;
  message?: string;
}



// New visibility controls (scoring visibility and showcase)
export type ScoringVisibilityMode = 'always_visible' | 'creator_controlled';

export interface LeagueUpdateVisibilitySettings {
  default_visible_for_new_rounds: boolean;
}

export interface RoundVisibilityRecord {
  league_id: number;
  season_id: number;
  round_number: number;
  visible: boolean;
  visible_at: string | null; // ISO 8601
}

export interface RoundVisibilityResponse {
  data: RoundVisibilityRecord;
}

export interface RoundVisibilityListResponse {
  data: Array<RoundVisibilityRecord & { showcased_user_ids: number[] }>;
}

export interface PublishBulkRequest {
  season_id?: number;
  round_numbers: number[];
}

export interface ShowcaseUsersRequest {
  season_id?: number;
  user_ids: number[];
}

export interface ShowcaseState {
  league_id: number;
  season_id: number;
  round_number: number;
  showcased_user_ids: number[];
}

export interface UserRoundScore {
  user_id: number;
  username: string;
  round_number: number;
  total_points: number;
  breakdown: Array<{ match_id: number; points: number }>;
}

export interface LeagueCreateOrUpdate {
  name?: string;
  competition_id?: number;
  open?: boolean;
  season_id?: number;
  starting_matchday?: number;
  youtube_league?: boolean;
  youtube_channel_id?: string;
  subscriber_only?: boolean;
  min_subscriber_date?: string;
  subscriber_requirement_type?: string;
  unsubscribe_policy?: string;
  scoring_visibility_mode?: ScoringVisibilityMode;
  showcase_user_limit?: number; // > 0
}
