{"openapi": "3.0.0", "info": {"title": "Brag Rights YouTube OAuth API", "version": "1.0.0", "description": "API contracts for YouTube OAuth integration with progressive authentication and subscriber-only leagues"}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "Development server"}, {"url": "https://api.bragrights.football/api/v1", "description": "Production server"}], "security": [{"bearerAuth": []}], "paths": {"/youtube_auth/status": {"get": {"summary": "Get YouTube connection status", "description": "Returns current YouTube connection status and OAuth scope information", "tags": ["YouTube Auth"], "responses": {"200": {"description": "YouTube status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/YouTubeStatus"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/youtube_auth/scope_upgrade_url": {"get": {"summary": "Generate OAuth URL for scope upgrade", "description": "Generates OAuth URL for progressive scope upgrade to include YouTube permissions", "tags": ["YouTube Auth"], "parameters": [{"name": "redirect_uri", "in": "query", "required": true, "schema": {"type": "string", "format": "uri"}, "description": "OAuth callback <PERSON><PERSON>"}], "responses": {"200": {"description": "OAuth URL generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthURL"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/youtube_auth/upgrade_scope": {"post": {"summary": "Complete OAuth scope upgrade", "description": "Completes OAuth scope upgrade after user authorization", "tags": ["YouTube Auth"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScopeUpgradeRequest"}}}}, "responses": {"200": {"description": "OAuth scope upgraded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScopeUpgradeResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/youtube_auth/verify_subscription": {"post": {"summary": "Verify channel subscription", "description": "Verifies if user is subscribed to a specific YouTube channel", "tags": ["YouTube Auth"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionVerificationRequest"}}}}, "responses": {"200": {"description": "Subscription status retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionVerificationResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/youtube/subscription_status": {"get": {"summary": "Check subscription status", "description": "Checks subscription status for a specific YouTube channel", "tags": ["YouTube Controller"], "parameters": [{"name": "channel_id", "in": "query", "required": true, "schema": {"type": "string"}, "description": "YouTube channel ID"}], "responses": {"200": {"description": "Subscription status retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionStatusResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/youtube/update_creator_status": {"post": {"summary": "Update content creator status", "description": "Enables or disables content creator mode for verified YouTube users", "tags": ["YouTube Controller"], "responses": {"200": {"description": "Creator status updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatorStatusResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/youtube/check_subscriber_league_eligibility": {"get": {"summary": "Check subscriber league eligibility", "description": "Checks if user is eligible to create subscriber-only leagues", "tags": ["YouTube Controller"], "responses": {"200": {"description": "Eligibility status retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EligibilityResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"YouTubeStatus": {"type": "object", "required": ["connected", "is_content_creator", "has_basic_scope", "has_youtube_scope", "needs_scope_upgrade", "can_use_youtube_features"], "properties": {"connected": {"type": "boolean", "description": "Whether user has connected YouTube account"}, "is_content_creator": {"type": "boolean", "description": "Whether user has enabled content creator mode"}, "channel_id": {"type": "string", "nullable": true, "description": "YouTube channel ID"}, "channel_name": {"type": "string", "nullable": true, "description": "YouTube channel name"}, "avatar_url": {"type": "string", "nullable": true, "description": "YouTube channel avatar URL"}, "subscriber_count": {"type": "integer", "nullable": true, "description": "YouTube channel subscriber count"}, "verified_at": {"type": "string", "nullable": true, "format": "date-time", "description": "When YouTube account was verified"}, "has_basic_scope": {"type": "boolean", "description": "Whether user has basic OAuth scope"}, "has_youtube_scope": {"type": "boolean", "description": "Whether user has YouTube OAuth scope"}, "needs_scope_upgrade": {"type": "boolean", "description": "Whether user needs to upgrade OAuth scope"}, "can_use_youtube_features": {"type": "boolean", "description": "Whether user can use YouTube features"}}}, "OAuthURL": {"type": "object", "required": ["oauth_url", "state", "scopes"], "properties": {"oauth_url": {"type": "string", "format": "uri", "description": "OAuth authorization URL"}, "state": {"type": "string", "minLength": 32, "maxLength": 32, "description": "OAuth state parameter for CSRF protection"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "OAuth scopes being requested"}}}, "ScopeUpgradeRequest": {"type": "object", "required": ["code", "redirect_uri", "state"], "properties": {"code": {"type": "string", "description": "Authorization code from OAuth callback"}, "redirect_uri": {"type": "string", "format": "uri", "description": "OAuth callback <PERSON><PERSON>"}, "state": {"type": "string", "description": "OAuth state parameter"}}}, "ScopeUpgradeResponse": {"type": "object", "required": ["success", "message", "scopes"], "properties": {"success": {"type": "boolean", "enum": [true]}, "message": {"type": "string", "description": "Success message"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Updated OAuth scopes"}}}, "SubscriptionVerificationRequest": {"type": "object", "required": ["channel_id"], "properties": {"channel_id": {"type": "string", "description": "YouTube channel ID to check subscription for"}}}, "SubscriptionVerificationResponse": {"type": "object", "required": ["status", "connected", "subscribed"], "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "connected": {"type": "boolean", "description": "Whether user has YouTube account connected"}, "subscribed": {"type": "boolean", "description": "Whether user is subscribed to the channel"}, "message": {"type": "string", "nullable": true, "description": "Status message"}, "needs_scope_upgrade": {"type": "boolean", "nullable": true, "description": "Whether user needs OAuth scope upgrade"}}}, "SubscriptionStatusResponse": {"type": "object", "required": ["status", "connected", "subscribed"], "properties": {"status": {"type": "string", "enum": ["success", "error"]}, "connected": {"type": "boolean"}, "subscribed": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "needs_scope_upgrade": {"type": "boolean", "nullable": true}, "channel": {"type": "object", "nullable": true, "properties": {"id": {"type": "string"}}}}}, "CreatorStatusResponse": {"type": "object", "required": ["message", "is_content_creator"], "properties": {"message": {"type": "string", "description": "Status update message"}, "is_content_creator": {"type": "boolean", "nullable": true, "description": "Updated content creator status"}}}, "EligibilityResponse": {"type": "object", "required": ["eligible", "min_subscribers_required"], "properties": {"eligible": {"type": "boolean", "description": "Whether user is eligible for subscriber leagues"}, "subscriber_count": {"type": "integer", "nullable": true, "description": "User's current subscriber count"}, "min_subscribers_required": {"type": "integer", "minimum": 1, "description": "Minimum subscribers required"}, "reason": {"type": "string", "nullable": true, "description": "Reason for ineligibility"}, "message": {"type": "string", "nullable": true, "description": "Status message"}}}, "Error": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "Error message"}}}}, "responses": {"BadRequest": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "Unauthorized": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "UnprocessableEntity": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}