export type HexColor = `#${string}`;

export interface LeagueCustomizationColorScheme {
  primaryBg?: HexColor;
  secondaryBg?: HexColor;
  primaryText?: HexColor;
  secondaryText?: HexColor;
  accent?: HexColor;
  tabBg?: HexColor;
  tabActive?: HexColor;
  headerBg?: HexColor;
}

export type HeaderPlacement = 'above_league_name' | 'below_league_name' | 'above_tabs';
export type LogoPosition = 'left' | 'center' | 'right';
export type LogoSize = 'small' | 'medium' | 'large';

export interface LeagueCustomizationResponse {
  leagueId: number;
  canCustomizeLeague: boolean;
  customHeader?: string;
  headerFont?: string;
  headerPlacement?: HeaderPlacement;
  logoUrl?: string;
  logoPosition?: LogoPosition;
  logoSize?: LogoSize;
  colorScheme: LeagueCustomizationColorScheme;
}

export interface LeagueCustomizationRequest {
  customization: {
    customHeader?: string;
    headerFont?: string;
    headerPlacement?: HeaderPlacement;
    logoUrl?: string;
    logoPosition?: LogoPosition;
    logoSize?: LogoSize;
    colorScheme?: LeagueCustomizationColorScheme;
  };
}

