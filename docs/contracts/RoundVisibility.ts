// Round Visibility Contracts
export interface RoundVisibilityDefaultsRequest {
  default_visible_for_new_rounds: boolean;
}

export interface RoundVisibilityDefaultsResponse {
  data: {
    league_id: number;
    default_visible_for_new_rounds: boolean;
  };
}

export interface PublishRoundResponse {
  data: {
    league_id: number;
    season_id: number;
    round_number: number;
    visible: true;
    visible_at: string; // ISO 8601
  };
}

export type ViewPermission = 'all' | 'self' | 'owner' | 'owner_showcase';

export interface RoundVisibilityInfoPerRound {
  round_number: number;
  visible: boolean;
  finished: boolean;
  top_performers_available: boolean;
  league_showcased_users_available: boolean;
  view_permission: ViewPermission;
}
export interface RoundVisibilityInfoResponse {
  data: {
    league_id: number;
    season_id: number;
    published_round_numbers: number[];
    unpublished_round_numbers: number[];
    rounds_with_showcase_data: number[];
    per_round: RoundVisibilityInfoPerRound[];
  };
}
