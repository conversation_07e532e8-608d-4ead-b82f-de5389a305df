// YouTube-related contracts used by the frontend

export interface QuotaStatus {
  can_authenticate: boolean;
  is_limited: boolean;
  usage_percentage: number;
  current_usage: number;
  daily_limit: number;
  remaining_quota: number;
  message: 'temporarily_limited' | 'high_usage_warning' | 'fully_available';
}

export interface YouTubeAuthStatus {
  connected: boolean;
  is_content_creator: boolean;
  channel_id?: string | null;
  channel_name?: string | null;
  avatar_url?: string | null;
  subscriber_count?: number | null;
  verified_at?: string | null; // ISO timestamp
  has_basic_scope: boolean;
  has_youtube_scope: boolean;
  needs_scope_upgrade: boolean;
  can_use_youtube_features: boolean;
  quota_status: QuotaStatus;
}

export interface QuotaExhaustedError {
  error: 'YOUTUBE_QUOTA_EXHAUSTED';
  message?: string;
}

export interface NotSubscribedError {
  error: 'YOUTUBE_NOT_SUBSCRIBED';
  message?: string;
}

export type YouTubeJoinErrors = QuotaExhaustedError | NotSubscribedError;

