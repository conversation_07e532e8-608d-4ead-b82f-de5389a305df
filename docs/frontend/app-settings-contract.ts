// Admin AppSettings API contracts
// Source of truth: docs/api/admin_app_settings_openapi.yaml

export interface AppSetting {
  key: string;
  value: string;
}

export interface AppSettingWrite {
  key: string;
  value: string; // numeric settings must be integer strings (>= 1)
}

export interface ApiError {
  error: string;
  message?: string;
}

export type LeagueJoinLimitKey =
  | 'default_league_join_limit'
  | 'content_creator_league_join_limit';

export function parseIntSetting(value: string, fallback: number): number {
  const n = Number(value);
  if (!Number.isInteger(n) || n < 1) return fallback;
  return n;
}

