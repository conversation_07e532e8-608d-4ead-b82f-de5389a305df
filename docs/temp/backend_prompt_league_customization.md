# Backend Prompt: League Customization, Visibility, and Round Scoring

## Scope

Implement production-ready endpoints and persistence for:

- League Customization (GET/PUT)
- League Visibility (PATCH)
- Round Scoring read endpoints (GET league round scores, winners, and user round score)
- Include `can_customize_league` boolean where appropriate

Follow the OpenAPI contracts already provided in:

- docs/copied_from_be/leagues_customization_openapi.yaml
- docs/copied_from_be/leagues_visibility_openapi.yaml
- docs/copied_from_be/round_scoring_openapi.yaml

## Authorization and `can_customize_league`

- A user can customize a league only if BOTH are true:
  1. The user is the owner of the league
  2. The user has YouTube content creator status (existing BE flag)
- Expose `can_customize_league: boolean` in responses so FE can gate the UI without extra calls:
  - Include in GET /leagues/{league_id}/customization response body (top-level field)
  - Optionally include in the standard league detail response as well (if available)

## Endpoints

1. GET /api/v1/leagues/{league_id}/customization

   - Response: LeagueCustomizationResponse (see OpenAPI)
   - Add top-level `can_customize_league: boolean`
   - 200 when found; 404 when league missing; 401 when unauthenticated

2. PUT /api/v1/leagues/{league_id}/customization

   - Request: LeagueCustomizationRequest
   - AuthZ: Only league owner AND content creator
   - 200 returns updated LeagueCustomizationResponse; 403 otherwise; 422 on validation errors

3. PATCH /api/v1/leagues/{id}/visibility

   - Request: UpdateLeagueVisibilityRequest { standings_visible?: boolean; round_scores_visible?: boolean }
   - AuthZ: Only league owner AND content creator
   - 200 returns { data: LeagueVisibility }
   - 403 when not authorized (use error_key NOT_AUTHORIZED or NOT_CONTENT_CREATOR as per spec)

4. GET /api/v1/leagues/{id}/rounds/{round_number}/scores

   - Returns LeagueRoundScoresResponse
   - If owner has hidden round scores (`round_scores_visible=false`), return 403 with error_key LEAGUE_ROUND_SCORES_HIDDEN

5. GET /api/v1/leagues/{id}/rounds/{round_number}/winners

   - Returns LeagueRoundWinnersResponse
   - Same visibility rule as above

6. GET /api/v1/users/{id}/rounds/{round_number}/score[?season_id]
   - Returns UserRoundScoreResponse or 404 if none

Note: Round endpoints must also be available under /api/v1/youtube/... aliases if documented.

## Database Schema

Tables (minimal suggestion; align with existing conventions):

- leagues (existing)
- users (existing)
- youtube_profiles (existing: to determine content creator status)

- league_customizations

  - id (pk)
  - league_id (fk -> leagues.id, unique)
  - custom_header varchar(120) nullable
  - header_font varchar(100) nullable
  - header_placement enum('above_league_name','below_league_name','above_tabs') nullable
  - logo_url text nullable
  - logo_position enum('left','center','right') nullable
  - logo_size enum('small','medium','large') nullable
  - color_primary_bg char(7) nullable
  - color_secondary_bg char(7) nullable
  - color_primary_text char(7) nullable
  - color_secondary_text char(7) nullable
  - color_accent char(7) nullable
  - color_tab_bg char(7) nullable
  - color_tab_active char(7) nullable
  - color_header_bg char(7) nullable
  - created_at, updated_at timestamptz

- league_visibility
  - id (pk)
  - league_id (fk -> leagues.id, unique)
  - standings_visible boolean not null default true
  - round_scores_visible boolean not null default true
  - created_at, updated_at timestamptz

## Validation Rules

- custom_header: max 120 chars
- header_placement: enum as per OpenAPI
- logo_url: must be a valid absolute URL (http/https)
- logo_position: enum
- logo_size: enum
- Colors: must be hex of form #RRGGBB (normalize by adding leading # and uppercasing). Reject invalid strings with 422 and error list.
- Request bodies must not include additionalProperties beyond the schema (reject with 422 where applicable).

## Response Mapping & Casing

- BE stores fields in snake_case
- FE expects camelCase; FE service layer maps snake_case to camelCase
- Align response shape with OpenAPI LeagueCustomizationResponse and ColorScheme fields
- Include top-level `can_customize_league` boolean in customization GET response

## Authorization Logic Detail

- league_owner = (current_devise_api_user.id == leagues.owner_id)
- is_content_creator = (youtube_profiles.content_creator_enabled == true) for current_user
- can_customize_league = league_owner && is_content_creator
- For PUT customization and PATCH visibility: require can_customize_league; otherwise 403

## Integration Points

- Ensure /leagues/{league_id}/customization GET is fast and cached; consider ETag/Last-Modified
- PUT must upsert into league_customizations for that league_id
- PATCH visibility must upsert into league_visibility for that league_id
- Round scoring endpoints should read visibility and enforce 403 when hidden
- Existing league endpoints should not break; if possible, add can_customize_league to league detail for FE convenience

## Error Format

- Use consistent JSON error envelopes with `error` and `error_key` where applicable
- 403 examples: NOT_AUTHORIZED, NOT_CONTENT_CREATOR
- 403 for hidden round scores: LEAGUE_ROUND_SCORES_HIDDEN
- 422: return `{ errors: string[] }` per customization OpenAPI

## Testing

- Unit tests for authz and validators
- Integration tests for:
  - can_customize_league true/false cases
  - PUT customization creates/updates rows
  - PATCH visibility toggles both flags
  - Round scoring honors visibility
- Include at least one invalid color and invalid URL test case producing 422

## Notes

- FE is now wired to:
  - GET + PUT /leagues/{league_id}/customization (camelCase mapped client-side)
  - PATCH /leagues/{id}/visibility (standings_visible, round_scores_visible)
  - GET round scoring endpoints (scores, winners, user score)
- Please ping FE if any field names must change so we can update the mapper.
