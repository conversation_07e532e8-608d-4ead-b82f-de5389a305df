---
type: "agent_requested"
description: "Automatically applies when working with API endpoints, REST routes, HTTP controllers, Express routes, FastAPI endpoints, services, models, or any web service development. Triggers when detecting files with routes, endpoints, controllers, database models, or API-related code."
---

# Auto API Standards Rules

description: "Automatically applies when working with API endpoints, routes, controllers, or HTTP services"

## Automatic Documentation Generation

When Augment detects API-related work, automatically:

1. **Generate OpenAPI specs** with:

   - Complete parameter definitions
   - All response schemas
   - Error code documentation
   - Authentication requirements
   - Rate limiting information

2. **Create TypeScript contracts**:

   - Request/response interfaces
   - Enum definitions for constants
   - Union types for different response states
   - Generic types for reusable patterns

3. **Provide frontend integration**:
   - HTTP client examples (fetch, axios)
   - React hook patterns
   - Error boundary implementations
   - Loading state management

## API Design Standards

### Endpoint Naming

- Use RESTful conventions
- Consistent URL structures
- Proper HTTP methods
- Clear resource naming

### Response Format

```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: ValidationError[];
  meta?: {
    timestamp: string;
    requestId: string;
  };
}
```

### Error Handling

- Consistent error response format
- Proper HTTP status codes
- Detailed error messages for development
- User-friendly messages for production

## Documentation Templates

### Endpoint Documentation Template

```markdown
# [Method] /api/resource

## Description

[What this endpoint does]

## Request

[Request schema and examples]

## Response

[Response schema and examples]

## Frontend Usage

[TypeScript example with error handling]

## Notes

[Implementation details, caveats, performance considerations]
```

## Validation Rules

- All endpoints must have OpenAPI documentation
- All request/response types must be defined
- All error cases must be documented
- Frontend examples must include error handling
- Performance impact must be noted for complex operations
