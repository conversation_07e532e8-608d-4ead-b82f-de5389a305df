---
type: "always_apply"
description: "Example description"
---

# Always Documentation Rules

This rule applies to every interaction with Augment AI.

## Documentation Requirements

1. **ALWAYS** create documentation in the `docs/` folder for:

   - New endpoints
   - Service modifications
   - API changes
   - Database schema updates

2. **EVERY endpoint must have**:

   - Complete OpenAPI 3.0+ specification
   - TypeScript interface definitions
   - Frontend usage examples
   - Error response documentation
   - Contract definition

3. **Documentation format**:

   - Use markdown for human-readable docs
   - Use YAML for OpenAPI specs
   - Use TypeScript for interface definitions
   - Include practical code examples

4. **Frontend considerations**:

   - Always include return type descriptions
   - Provide async/await examples
   - Document loading and error states
   - Show data transformation patterns

5. **Quality standards**:
   - Documentation must be complete before code review
   - OpenAPI specs must validate
   - Examples must be tested and working
   - Keep docs in sync with implementation

## Response Pattern

When creating or modifying APIs, always respond with:

1. The code implementation
2. OpenAPI specification
3. TypeScript interfaces
4. Frontend usage example
5. Documentation file location

## Templates and References

- API Contract Template: `docs/templates/api-contract-template.ts`
- OpenAPI Template: `docs/templates/openapi-spec-template.yaml`
- Documentation Template: `docs/templates/endpoint-documentation-template.md`

## File Structure

```
docs/
├── templates/          # Reference templates for new APIs
├── api/
│   ├── endpoints/
│   └── schemas/
├── services/
├── contracts/          # Generated TypeScript contracts
├── tests/              # Testing documentation and examples
│   ├── patterns/       # Common testing patterns
│   ├── mocks/          # Mock configurations
│   └── fixtures/       # Test data and factories
└── examples/
    ├── frontend/
    └── testing/        # Testing examples
```
