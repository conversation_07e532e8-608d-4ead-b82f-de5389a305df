class UpdateLeagueCustomizationsColorScheme < ActiveRecord::Migration[7.0]
  def up
    # Add new color fields
    add_column :league_customizations, :primary, :string, limit: 32
    add_column :league_customizations, :primary_foreground, :string, limit: 32
    add_column :league_customizations, :secondary, :string, limit: 32
    add_column :league_customizations, :secondary_foreground, :string, limit: 32
    add_column :league_customizations, :accent_foreground, :string, limit: 32
    add_column :league_customizations, :highlight, :string, limit: 32
    add_column :league_customizations, :highlight_foreground, :string, limit: 32
    add_column :league_customizations, :muted, :string, limit: 32
    add_column :league_customizations, :muted_foreground, :string, limit: 32
    add_column :league_customizations, :destructive, :string, limit: 32
    add_column :league_customizations, :destructive_foreground, :string, limit: 32

    # Migrate existing data
    execute <<~SQL
      UPDATE league_customizations SET
        "primary" = primary_bg,
        primary_foreground = primary_text,
        "secondary" = secondary_bg,
        secondary_foreground = secondary_text,
        accent_foreground = secondary_text,
        highlight = tab_active,
        highlight_foreground = primary_text,
        "muted" = tab_bg,
        muted_foreground = secondary_text,
        destructive = accent,
        destructive_foreground = primary_text
      WHERE primary_bg IS NOT NULL#{' '}
         OR primary_text IS NOT NULL#{' '}
         OR secondary_bg IS NOT NULL#{' '}
         OR secondary_text IS NOT NULL#{' '}
         OR accent IS NOT NULL#{' '}
         OR tab_bg IS NOT NULL#{' '}
         OR tab_active IS NOT NULL;
    SQL

    # Remove old color fields
    remove_column :league_customizations, :primary_bg
    remove_column :league_customizations, :secondary_bg
    remove_column :league_customizations, :primary_text
    remove_column :league_customizations, :secondary_text
    remove_column :league_customizations, :tab_bg
    remove_column :league_customizations, :tab_active
    remove_column :league_customizations, :header_bg
  end

  def down
    # Add back old color fields
    add_column :league_customizations, :primary_bg, :string, limit: 32
    add_column :league_customizations, :secondary_bg, :string, limit: 32
    add_column :league_customizations, :primary_text, :string, limit: 32
    add_column :league_customizations, :secondary_text, :string, limit: 32
    add_column :league_customizations, :tab_bg, :string, limit: 32
    add_column :league_customizations, :tab_active, :string, limit: 32
    add_column :league_customizations, :header_bg, :string, limit: 32

    # Migrate data back
    execute <<~SQL
      UPDATE league_customizations SET
        primary_bg = "primary",
        primary_text = primary_foreground,
        secondary_bg = "secondary",
        secondary_text = secondary_foreground,
        tab_bg = "muted",
        tab_active = highlight,
        header_bg = "secondary"
      WHERE "primary" IS NOT NULL#{' '}
         OR primary_foreground IS NOT NULL#{' '}
         OR "secondary" IS NOT NULL#{' '}
         OR secondary_foreground IS NOT NULL;
    SQL

    # Remove new color fields
    remove_column :league_customizations, :primary
    remove_column :league_customizations, :primary_foreground
    remove_column :league_customizations, :secondary
    remove_column :league_customizations, :secondary_foreground
    remove_column :league_customizations, :accent_foreground
    remove_column :league_customizations, :highlight
    remove_column :league_customizations, :highlight_foreground
    remove_column :league_customizations, :muted
    remove_column :league_customizations, :muted_foreground
    remove_column :league_customizations, :destructive
    remove_column :league_customizations, :destructive_foreground
  end
end
