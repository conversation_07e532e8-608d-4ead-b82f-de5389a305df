class CreateLeagueRoundVisibilitySettings < ActiveRecord::Migration[7.0]
  def change
    create_table :league_round_visibility_settings do |t|
      t.references :league, null: false, foreign_key: true, index: { unique: true }
      t.boolean :default_visible_for_new_rounds, null: false, default: true
      t.timestamps
    end

    # Backfill from existing leagues.round_scores_visible if present
    reversible do |dir|
      dir.up do
        if column_exists?(:leagues, :round_scores_visible)
          execute <<~SQL
            INSERT INTO league_round_visibility_settings (league_id, default_visible_for_new_rounds, created_at, updated_at)
            SELECT id, COALESCE(round_scores_visible, TRUE), NOW(), NOW() FROM leagues
            ON CONFLICT (league_id) DO NOTHING;
          SQL
        else
          execute <<~SQL
            INSERT INTO league_round_visibility_settings (league_id, default_visible_for_new_rounds, created_at, updated_at)
            SELECT id, TRUE, NOW(), NOW() FROM leagues
            ON CONFLICT (league_id) DO NOTHING;
          SQL
        end
      end
    end
  end
end

