class ExpandColorFieldsInLeagueCustomizations < ActiveRecord::Migration[6.1]
  def change
    change_column :league_customizations, :primary_bg, :string, limit: 32
    change_column :league_customizations, :secondary_bg, :string, limit: 32
    change_column :league_customizations, :primary_text, :string, limit: 32
    change_column :league_customizations, :secondary_text, :string, limit: 32
    change_column :league_customizations, :accent, :string, limit: 32
    change_column :league_customizations, :tab_bg, :string, limit: 32
    change_column :league_customizations, :tab_active, :string, limit: 32
    change_column :league_customizations, :header_bg, :string, limit: 32
  end
end
