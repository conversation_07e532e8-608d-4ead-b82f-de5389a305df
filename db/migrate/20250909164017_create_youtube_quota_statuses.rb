class CreateYoutubeQuotaStatuses < ActiveRecord::Migration[7.0]
  def change
    create_table :youtube_quota_statuses do |t|
      t.date :quota_date, null: false
      t.integer :current_usage, default: 0, null: false
      t.integer :daily_limit, default: 10000, null: false
      t.integer :buffer_threshold, default: 1000, null: false
      t.boolean :quota_exhausted, default: false, null: false
      t.datetime :exhausted_at
      t.text :last_exhaustion_reason
      t.timestamps

      # Ensure we only have one record per date
      t.index :quota_date, unique: true
    end

    # Add an index for quick lookups of current status
    add_index :youtube_quota_statuses, [:quota_date, :quota_exhausted], 
              name: 'index_youtube_quota_statuses_on_date_and_exhausted'
  end
end