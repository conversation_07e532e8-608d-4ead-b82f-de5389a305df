class CreateLeagueRoundVisibilities < ActiveRecord::Migration[7.0]
  def change
    create_table :league_round_visibilities do |t|
      t.references :league, null: false, foreign_key: true
      t.bigint :season_id, null: false
      t.integer :round_number, null: false
      t.boolean :visible, null: false, default: true
      t.datetime :visible_at, null: false
      t.bigint :made_visible_by_user_id, null: false
      t.timestamps
    end

    add_index :league_round_visibilities, [:league_id, :season_id, :round_number], unique: true, name: 'idx_unique_lrv_on_league_season_round'
    add_foreign_key :league_round_visibilities, :seasons, column: :season_id
    add_foreign_key :league_round_visibilities, :users, column: :made_visible_by_user_id
  end
end

