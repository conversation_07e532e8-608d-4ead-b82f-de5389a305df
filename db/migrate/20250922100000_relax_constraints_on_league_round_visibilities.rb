class RelaxConstraintsOnLeagueRoundVisibilities < ActiveRecord::Migration[7.0]
  def up
    change_column_null :league_round_visibilities, :visible_at, true
    change_column_null :league_round_visibilities, :made_visible_by_user_id, true
    change_column_default :league_round_visibilities, :visible, from: true, to: false
  end

  def down
    # Be conservative on rollback: set published rows' fields if missing, then restore NOT NULL and default
    execute <<~SQL
      UPDATE league_round_visibilities
      SET visible_at = NOW()
      WHERE visible = TRUE AND visible_at IS NULL;
    SQL

    # Fallback to league owner as publisher if missing (best effort)
    execute <<~SQL
      UPDATE league_round_visibilities lrv
      SET made_visible_by_user_id = leagues.owner_id
      FROM leagues
      WHERE lrv.league_id = leagues.id
        AND lrv.visible = TRUE
        AND lrv.made_visible_by_user_id IS NULL;
    SQL

    change_column_null :league_round_visibilities, :visible_at, false
    change_column_null :league_round_visibilities, :made_visible_by_user_id, false
    change_column_default :league_round_visibilities, :visible, from: false, to: true
  end
end

