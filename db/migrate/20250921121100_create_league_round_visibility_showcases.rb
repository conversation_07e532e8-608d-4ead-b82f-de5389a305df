# frozen_string_literal: true

class CreateLeagueRoundVisibilityShowcases < ActiveRecord::Migration[7.0]
  def change
    create_table :league_round_visibility_showcases do |t|
      t.references :league_round_visibility, null: false, foreign_key: true,
                                             index: { name: 'idx_lrv_showcases_on_lrv_id' }
      t.references :user, null: false, foreign_key: true
      t.timestamps
    end

    add_index :league_round_visibility_showcases,
              %i[league_round_visibility_id user_id],
              unique: true,
              name: 'idx_unique_lrv_showcase_on_lrv_and_user'
  end
end
