class CreateLeagueCustomizations < ActiveRecord::Migration[7.0]
  def change
    create_table :league_customizations do |t|
      t.references :league, null: false, foreign_key: true, index: { unique: true }
      t.text :custom_header, limit: 120
      t.string :header_font, limit: 64
      t.string :header_placement, limit: 32
      t.text :logo_url
      t.string :logo_position, limit: 16
      t.string :logo_size, limit: 16
      t.string :primary_bg, limit: 7
      t.string :secondary_bg, limit: 7
      t.string :primary_text, limit: 7
      t.string :secondary_text, limit: 7
      t.string :accent, limit: 7
      t.string :tab_bg, limit: 7
      t.string :tab_active, limit: 7
      t.string :header_bg, limit: 7

      t.timestamps
    end
  end
end

