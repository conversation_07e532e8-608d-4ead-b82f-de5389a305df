# Custom loggers for better debugging and monitoring
# These loggers provide dedicated logging for specific areas of the application

module CustomLoggers
  class << self
    # Match logger for match state changes, score updates, status transitions
    def match_logger
      @match_logger ||= create_logger('match', 'match_operations.log')
    end

    # Predictions logger for match and round predictions
    def predictions_logger
      @predictions_logger ||= create_logger('predictions', 'predictions.log')
    end

    # Background jobs logger for job execution, failures, retries
    def background_jobs_logger
      @background_jobs_logger ||= create_logger('background_jobs', 'background_jobs.log')
    end

    # League customization logger for league settings changes
    def league_customization_logger
      @league_customization_logger ||= create_logger('league_customization', 'league_customization.log')
    end

    # Round showcase logger for showcase user selections and visibility changes
    def round_showcase_logger
      @round_showcase_logger ||= create_logger('round_showcase', 'round_showcase.log')
    end

    private

    def create_logger(name, filename)
      log_file = File.join(Rails.root, 'log', filename)
      logger = Logger.new(log_file, 'daily')
      logger.level = Rails.env.production? ? Logger::INFO : Logger::DEBUG
      logger.formatter = proc do |severity, datetime, _progname, msg|
        "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity} [#{name.upcase}] #{msg}\n"
      end
      logger
    end
  end
end

# Helper methods for structured logging
module LoggingHelpers
  # Log match state changes with context
  def log_match_change(match, action, details = {})
    context = {
      match_id: match.id,
      competition: match.season.competition.name,
      season_id: match.season_id,
      matchday: match.matchday,
      stage: match.stage,
      home_team: match.home_team.name,
      away_team: match.away_team.name,
      status: match.status,
      action:,
      timestamp: Time.current.iso8601
    }.merge(details)

    CustomLoggers.match_logger.info("Match #{action}: #{context.to_json}")
  end

  # Log prediction calculations with context
  def log_prediction_calculation(prediction, action, details = {})
    context = if prediction.is_a?(MatchPrediction)
                {
                  prediction_id: prediction.id,
                  match_id: prediction.match_id,
                  user_id: prediction.user_id,
                  predicted_score: "#{prediction.home_score}-#{prediction.away_score}",
                  points: prediction.points,
                  action:,
                  timestamp: Time.current.iso8601
                }.merge(details)
              else # RoundPrediction
                {
                  prediction_id: prediction.id,
                  user_id: prediction.user_id,
                  season_id: prediction.season_id,
                  matchday: prediction.matchday,
                  stage: prediction.stage,
                  total_points: prediction.total_points,
                  action:,
                  timestamp: Time.current.iso8601
                }.merge(details)
              end

    CustomLoggers.predictions_logger.info("Prediction #{action}: #{context.to_json}")
  end

  # Log background job execution with context
  def log_background_job(job_name, action, details = {})
    context = {
      job_name:,
      action:,
      timestamp: Time.current.iso8601,
      environment: Rails.env
    }.merge(details)

    CustomLoggers.background_jobs_logger.info("Job #{action}: #{context.to_json}")
  end

  # Log league customization changes
  def log_league_customization(league, action, details = {})
    context = {
      league_id: league.id,
      league_name: league.name,
      owner_id: league.owner_id,
      action:,
      timestamp: Time.current.iso8601
    }.merge(details)

    CustomLoggers.league_customization_logger.info("League customization #{action}: #{context.to_json}")
  end

  # Log round showcase changes
  def log_round_showcase(league, round_number, action, details = {})
    context = {
      league_id: league.id,
      league_name: league.name,
      season_id: league.season_id,
      round_number:,
      action:,
      timestamp: Time.current.iso8601
    }.merge(details)

    CustomLoggers.round_showcase_logger.info("Round showcase #{action}: #{context.to_json}")
  end
end

# Include logging helpers in ApplicationRecord for model access
class ApplicationRecord < ActiveRecord::Base
  include LoggingHelpers
  self.abstract_class = true
end

# Include logging helpers in ApplicationJob for job access
class ApplicationJob < ActiveJob::Base
  include LoggingHelpers
end

# Include logging helpers in ApplicationController for controller access
class ApplicationController < ActionController::API
  include LoggingHelpers
end
