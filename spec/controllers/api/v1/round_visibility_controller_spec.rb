require 'rails_helper'

RSpec.describe Api::V1::RoundVisibilityController, type: :controller do
  let(:area) { create(:area) }
  let(:user) { create(:user, is_content_creator: true) }
  let(:league_competition) { create(:competition, area: area, competition_type: 'LEAGUE') }
  let(:cup_competition) { create(:competition, area: area, competition_type: 'CUP') }
  let(:league_season) { create(:season, competition: league_competition) }
  let(:cup_season) { create(:season, competition: cup_competition, current_stage: 'LEAGUE_STAGE') }
  let(:league) { create(:league, competition: league_competition, season: league_season, owner: user) }
  let(:cup_league) { create(:league, competition: cup_competition, season: cup_season, owner: user) }

  before do
    sign_in user
  end

  describe 'GET #visibility_info' do
    context 'for league competitions' do
      let!(:team1) { create(:team) }
      let!(:team2) { create(:team) }
      let!(:match1) { create(:match, season: league_season, matchday: 1, stage: 'REGULAR_SEASON', home_team: team1, away_team: team2, status: 'FINISHED') }
      let!(:match2) { create(:match, season: league_season, matchday: 2, stage: 'REGULAR_SEASON', home_team: team2, away_team: team1, status: 'SCHEDULED') }

      it 'returns visibility info for league competitions' do
        get :visibility_info, params: { id: league.id }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        
        expect(json_response['data']['league_id']).to eq(league.id)
        expect(json_response['data']['season_id']).to eq(league_season.id)
        expect(json_response['data']['per_round']).to be_an(Array)
        
        # Check that round numbers match matchdays
        round_numbers = json_response['data']['per_round'].map { |r| r['round_number'] }
        expect(round_numbers).to contain_exactly(1, 2)
      end

      it 'includes new boolean fields in per_round data' do
        get :visibility_info, params: { id: league.id }

        json_response = JSON.parse(response.body)
        per_round = json_response['data']['per_round'].first

        expect(per_round).to have_key('can_unpublish')
        expect(per_round).to have_key('display_in_selector')
        expect(per_round['can_unpublish']).to be_in([true, false])
        expect(per_round['display_in_selector']).to be_in([true, false])
      end
    end

    context 'for cup competitions' do
      let!(:team1) { create(:team) }
      let!(:team2) { create(:team) }
      let!(:league_stage_match) { create(:match, season: cup_season, matchday: 1, stage: 'LEAGUE_STAGE', home_team: team1, away_team: team2, status: 'FINISHED') }
      let!(:playoff_match) { create(:match, season: cup_season, matchday: 1, stage: 'PLAYOFFS', home_team: team1, away_team: team2, status: 'SCHEDULED') }

      it 'returns visibility info for cup competitions with overall round numbering' do
        get :visibility_info, params: { id: cup_league.id }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        
        expect(json_response['data']['league_id']).to eq(cup_league.id)
        expect(json_response['data']['season_id']).to eq(cup_season.id)
        expect(json_response['data']['per_round']).to be_an(Array)
        
        # Cup competitions should use overall round numbering
        round_numbers = json_response['data']['per_round'].map { |r| r['round_number'] }
        expect(round_numbers).to be_an(Array)
        expect(round_numbers).not_to be_empty
      end

      it 'handles different stages correctly' do
        get :visibility_info, params: { id: cup_league.id }

        json_response = JSON.parse(response.body)
        expect(json_response['data']['per_round']).to be_an(Array)
        
        # Should include rounds from current and past stages only
        per_round = json_response['data']['per_round']
        expect(per_round).not_to be_empty
      end
    end
  end

  describe 'POST #unpublish' do
    let!(:lrv) { create(:league_round_visibility, league: league, season: league_season, round_number: 1, visible: true, visible_at: Time.current, made_visible_by_user: user) }

    context 'within grace period' do
      it 'successfully unpublishes the round' do
        post :unpublish, params: { id: league.id, round_number: 1 }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        
        expect(json_response['data']['visible']).to be false
        expect(json_response['data']['visible_at']).to be_nil
        
        lrv.reload
        expect(lrv.visible).to be false
        expect(lrv.visible_at).to be_nil
      end
    end

    context 'after grace period' do
      before { lrv.update_columns(visible_at: 25.hours.ago) }

      it 'returns grace period expired error' do
        post :unpublish, params: { id: league.id, round_number: 1 }

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        
        expect(json_response['error_key']).to eq('GRACE_PERIOD_EXPIRED')
      end
    end

    context 'when round visibility record does not exist' do
      it 'returns not found error' do
        post :unpublish, params: { id: league.id, round_number: 999 }

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        
        expect(json_response['error_key']).to eq('NOT_FOUND')
      end
    end

    context 'when user is not league owner' do
      let(:other_user) { create(:user, is_content_creator: true) }

      before { sign_in other_user }

      it 'returns unauthorized error' do
        post :unpublish, params: { id: league.id, round_number: 1 }

        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        
        expect(json_response['error_key']).to eq('LEAGUE_OWNER_ONLY')
      end
    end

    context 'when user is not content creator' do
      let(:regular_user) { create(:user, is_content_creator: false) }
      let(:regular_league) { create(:league, competition: league_competition, season: league_season, owner: regular_user) }

      before { sign_in regular_user }

      it 'returns forbidden error' do
        post :unpublish, params: { id: regular_league.id, round_number: 1 }

        expect(response).to have_http_status(:forbidden)
        json_response = JSON.parse(response.body)
        
        expect(json_response['error_key']).to eq('NOT_CONTENT_CREATOR')
      end
    end
  end

  describe 'POST #publish' do
    it 'logs the publish action' do
      expect_any_instance_of(Api::V1::RoundVisibilityController).to receive(:log_round_showcase)
        .with(league, 1, 'published', hash_including(:user_id, :season_id))

      post :publish, params: { id: league.id, round_number: 1 }
    end
  end
end
