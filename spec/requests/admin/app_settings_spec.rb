require 'rails_helper'

RSpec.describe 'Admin::AppSettings API', type: :request do
  let(:admin) { User.create!(email: '<EMAIL>', username: 'admin', password: 'Password1!', admin: true, confirmed_at: Time.current) }
  let(:token) { Devise::Api::Token.create!(resource_owner: admin, access_token: 'x'*64, refresh_token: 'y'*64, expires_in: 3600) }

  def auth_headers
    { 'Authorization' => "Bearer #{token.access_token}" }
  end

  describe 'GET /api/v1/admin/app_settings' do
    it 'lists settings' do
      AppSetting.create!(key: 'default_league_join_limit', value: '12')
      get '/api/v1/admin/app_settings', headers: auth_headers
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['data']).to be_an(Array)
    end
  end

  describe 'GET /api/v1/admin/app_settings/:key' do
    it 'shows a setting' do
      AppSetting.create!(key: 'content_creator_league_join_limit', value: '12')
      get '/api/v1/admin/app_settings/content_creator_league_join_limit', headers: auth_headers
      expect(response).to have_http_status(:ok)
      body = JSON.parse(response.body)
      expect(body.dig('data', 'key')).to eq('content_creator_league_join_limit')
    end
  end

  describe 'POST /api/v1/admin/app_settings' do
    it 'creates or updates a setting' do
      post '/api/v1/admin/app_settings', params: { app_setting: { key: 'default_league_join_limit', value: '10' } }, headers: auth_headers
      expect(response).to have_http_status(:created)
      expect(AppSetting.find_by(key: 'default_league_join_limit')&.value).to eq('10')
    end
  end

  describe 'PATCH /api/v1/admin/app_settings/:key' do
    it 'upserts by key' do
      patch '/api/v1/admin/app_settings/default_league_join_limit', params: { app_setting: { value: '15' } }, headers: auth_headers
      expect(response).to have_http_status(:ok)
      expect(AppSetting.find_by(key: 'default_league_join_limit')&.value).to eq('15')
    end
  end
end

