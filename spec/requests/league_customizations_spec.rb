# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'League Customizations API', type: :request do
  let(:owner) { create(:user, is_content_creator: true) }
  let(:non_owner) { create(:user, is_content_creator: true) }
  let(:regular_user) { create(:user, is_content_creator: false) }
  let(:league) { create(:league, owner:) }

  before do
    # Ensure callback creates default customization
    league.reload

    # Enable feature toggles for tests (allow all users)
    FeatureToggle.find_or_create_by!(name: 'League-customization') do |ft|
      ft.enabled = true
      ft.description = 'League header/logo customization'
      ft.allowed_user_ids = []
    end
    FeatureToggle.find_or_create_by!(name: 'League-customization-colorscheme') do |ft|
      ft.enabled = true
      ft.description = 'League color scheme customization'
      ft.allowed_user_ids = []
    end
  end

  describe 'GET /api/v1/leagues/:league_id/customization' do
    it 'returns customization for authenticated user' do
      token = create(:devise_api_token, resource_owner: owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      get("/api/v1/leagues/#{league.id}/customization", headers:)
      expect(response).to have_http_status(:ok)
      body = JSON.parse(response.body)
      expect(body['leagueId']).to eq(league.id)
      expect(body['colorScheme']).to be_a(Hash)
    end

    it 'requires authentication' do
      get "/api/v1/leagues/#{league.id}/customization"
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'PUT /api/v1/leagues/:league_id/customization' do
    let(:valid_payload) do
      {
        customization: {
          customHeader: 'Welcome!',
          headerPlacement: 'above_tabs',
          headerAlignment: 'center',
          headerFontSize: 18,
          logoUrl: 'https://cdn.example.com/logo.png',
          logoPosition: 'left',
          logoSize: 'medium',
          colorScheme: { primary: '#111111', primaryForeground: '#ffffff', accent: '#FFBF00' }
        }
      }
    end

    it 'updates customization for owner who is content creator' do
      token = create(:devise_api_token, resource_owner: owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      put("/api/v1/leagues/#{league.id}/customization", params: valid_payload, headers:)
      expect(response).to have_http_status(:ok)
      body = JSON.parse(response.body)
      expect(body['customHeader']).to eq('Welcome!')
      expect(body.dig('colorScheme', 'primary')).to eq('#111111')
    end

    it 'returns forbidden for non-owner content creator' do
      token = create(:devise_api_token, resource_owner: non_owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      put("/api/v1/leagues/#{league.id}/customization", params: valid_payload, headers:)
      expect(response).to have_http_status(:forbidden)
    end

    it 'returns forbidden for owner who is not content creator' do
      league.update!(owner: regular_user)
      token = create(:devise_api_token, resource_owner: regular_user)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      put("/api/v1/leagues/#{league.id}/customization", params: valid_payload, headers:)
      expect(response).to have_http_status(:forbidden)
    end

    it 'returns validation errors for bad colors' do
      token = create(:devise_api_token, resource_owner: owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      payload = valid_payload
      payload[:customization][:colorScheme][:primary] = 'not-a-color'
      put("/api/v1/leagues/#{league.id}/customization", params: payload, headers:)
      expect(response).to have_http_status(:unprocessable_entity)
      body = JSON.parse(response.body)
      expect(body['errors'].join).to match(/hex/i)
    end

    it 'forbids updating header/logo when feature disabled' do
      FeatureToggle.find_by(name: 'League-customization')&.update!(enabled: false)
      token = create(:devise_api_token, resource_owner: owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      payload = { customization: { customHeader: 'Nope' } }
      put("/api/v1/leagues/#{league.id}/customization", params: payload, headers:)
      expect(response).to have_http_status(:forbidden)
      body = JSON.parse(response.body)
      expect(body['error']).to eq('Feature not available')
    end

    it 'forbids updating color scheme when feature disabled' do
      FeatureToggle.find_by(name: 'League-customization-colorscheme')&.update!(enabled: false)
      token = create(:devise_api_token, resource_owner: owner)
      headers = { 'Authorization' => "Bearer #{token.access_token}" }
      payload = { customization: { colorScheme: { primary: '#000000' } } }
      put("/api/v1/leagues/#{league.id}/customization", params: payload, headers:)
      expect(response).to have_http_status(:forbidden)
      body = JSON.parse(response.body)
      expect(body['error']).to eq('Feature not available')
    end
  end

  def auth(user)
    token = create(:devise_api_token, resource_owner: user)
    headers = { 'Authorization' => "Bearer #{token.plaintext_token}" }
    allow_any_instance_of(ActionDispatch::Request).to receive(:headers).and_return(ActionDispatch::Http::Headers.from_hash(headers))
  end
end
