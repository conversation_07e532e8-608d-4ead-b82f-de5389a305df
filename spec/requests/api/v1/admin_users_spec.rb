require 'rails_helper'

RSpec.describe 'Admin Users', type: :request do
  let(:admin) { create(:user, :admin) }
  let(:admin_token) { create(:devise_api_token, resource_owner: admin) }
  let(:user) { create(:user) }

  it 'requires admin for index' do
    get '/api/v1/admin/users'
    expect(response).to have_http_status(:unauthorized)

    non_admin = create(:user)
    non_admin_token = create(:devise_api_token, resource_owner: non_admin)
    get '/api/v1/admin/users', headers: { 'Authorization' => "Bearer #{non_admin_token.access_token}" }
    expect(response).to have_http_status(:unauthorized)
  end

  it 'lists users for admin' do
    get '/api/v1/admin/users', headers: { 'Authorization' => "Bearer #{admin_token.access_token}" }
    expect(response).to have_http_status(:ok)
  end

  it 'soft deletes and restores a user' do
    delete "/api/v1/admin/users/#{user.id}", headers: { 'Authorization' => "Bearer #{admin_token.access_token}" }
    expect(response).to have_http_status(:ok)
    post "/api/v1/admin/users/#{user.id}/restore", headers: { 'Authorization' => "Bearer #{admin_token.access_token}" }
    expect(response).to have_http_status(:ok)
  end
end
