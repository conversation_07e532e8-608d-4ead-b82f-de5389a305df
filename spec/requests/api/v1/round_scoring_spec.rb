require 'rails_helper'

RSpec.describe 'Round scoring endpoints', type: :request do
  let(:user) { create(:user) }
  let(:league) { create(:league, owner: user) }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:round_number) { 1 }

  before do
    # Ensure membership
    league.memberships.find_or_create_by!(user:)
  end

  it 'requires auth for league scores' do
    get "/api/v1/youtube/leagues/#{league.id}/rounds/#{round_number}/scores"
    expect(response).to have_http_status(:unauthorized)
  end

  it 'returns league round scores' do
    get "/api/v1/youtube/leagues/#{league.id}/rounds/#{round_number}/scores",
        headers: { 'Authorization' => "Bearer #{token.access_token}" }
    expect([200, 404]).to include(response.status) # tolerant, data dependent
  end
end
