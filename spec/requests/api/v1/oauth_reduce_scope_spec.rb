require 'rails_helper'

RSpec.describe 'OAuth reduce scope', type: :request do
  let(:user) { create(:user) }
  let(:token) { create(:devise_api_token, resource_owner: user) }

  it 'requires auth' do
    patch '/api/v1/youtube_auth/reduce_scope', params: { scopes: %w[openid email profile] }
    expect(response).to have_http_status(:unauthorized)
  end

  it 'enforces one-way reduction policy' do
    creds = {
      access_token: 'x', refresh_token: 'r', expires_in: 3600, token_type: 'Bearer',
      scope: (YoutubeTokenService::BASIC_SCOPES + YoutubeTokenService::YOUTUBE_SCOPES).join(' ')
    }
    user.update!(youtube_credentials: creds.to_json)

    patch '/api/v1/youtube_auth/reduce_scope',
          headers: { 'Authorization' => "Bearer #{token.access_token}", 'CONTENT_TYPE' => 'application/json' },
          params: { scopes: YoutubeTokenService::BASIC_SCOPES }.to_json
    expect(response).to have_http_status(:ok)
    body = JSON.parse(response.body)
    expect(body['scopes']).to match_array(YoutubeTokenService::BASIC_SCOPES)
  end

  it 'rejects invalid targets' do
    creds = { access_token: 'x', refresh_token: 'r', expires_in: 3600, token_type: 'Bearer',
              scope: YoutubeTokenService::YOUTUBE_SCOPES.join(' ') }
    user.update!(youtube_credentials: creds.to_json)

    patch '/api/v1/youtube_auth/reduce_scope',
          headers: { 'Authorization' => "Bearer #{token.access_token}" },
          params: { scopes: %w[email profile] }
    expect(response).to have_http_status(:unprocessable_entity)
  end
end
