require 'rails_helper'

RSpec.describe 'League subscription requirement', type: :request do
  let(:owner) { create(:user) }
  let(:league) { create(:league, owner:) }
  let(:token) { create(:devise_api_token, resource_owner: owner) }

  it 'requires authentication' do
    patch "/api/v1/leagues/#{league.id}/subscription_requirement", params: { require_subscription: true }
    expect(response).to have_http_status(:unauthorized)
  end

  it 'rejects non-owner' do
    other = create(:user)
    other_token = create(:devise_api_token, resource_owner: other)
    patch "/api/v1/leagues/#{league.id}/subscription_requirement",
          headers: { 'Authorization' => "Bearer #{other_token.access_token}" },
          params: { require_subscription: true }
    expect(response).to have_http_status(:unauthorized)
  end

  it 'validates missing param' do
    patch "/api/v1/leagues/#{league.id}/subscription_requirement",
          headers: { 'Authorization' => "Bearer #{token.access_token}" }
    expect(response).to have_http_status(:bad_request)
    expect(JSON.parse(response.body)['errors']).to include('Missing parameter: require_subscription')
  end

  it 'enables subscriber_only with validations' do
    league.update!(youtube_channel_id: 'UC123')
    allow(YoutubeEligibilityService).to receive(:can_create_subscriber_league?).and_return(true)

    patch "/api/v1/leagues/#{league.id}/subscription_requirement",
          headers: { 'Authorization' => "Bearer #{token.access_token}" },
          params: { require_subscription: true }
    expect(response).to have_http_status(:ok)
    expect(JSON.parse(response.body)['subscriber_only']).to eq(true)
  end

  it 'disables subscriber_only' do
    league.update!(subscriber_only: true)
    patch "/api/v1/leagues/#{league.id}/subscription_requirement",
          headers: { 'Authorization' => "Bearer #{token.access_token}" },
          params: { require_subscription: false }
    expect(response).to have_http_status(:ok)
    expect(JSON.parse(response.body)['subscriber_only']).to eq(false)
  end
end
