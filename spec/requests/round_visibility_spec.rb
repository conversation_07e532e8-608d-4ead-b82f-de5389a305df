require 'rails_helper'

RSpec.describe 'Round Visibility API', type: :request do
  let(:owner) { create(:user) }
  let(:member) { create(:user) }
  let(:league) { create(:league, owner:) }
  let(:owner_token) { create(:devise_api_token, resource_owner: owner) }
  let(:member_token) { create(:devise_api_token, resource_owner: member) }
  let(:round_number) { 2 }

  before do
    league.memberships.find_or_create_by!(user: owner)
    league.memberships.find_or_create_by!(user: member)
  end

  describe 'PATCH /api/v1/leagues/:id/round_visibility_defaults' do
    it 'requires auth' do
      patch "/api/v1/leagues/#{league.id}/round_visibility_defaults", params: { default_visible_for_new_rounds: false }
      expect(response).to have_http_status(:unauthorized)
    end

    it 'requires content creator' do
      patch "/api/v1/leagues/#{league.id}/round_visibility_defaults", params: { default_visible_for_new_rounds: false },
                                                                      headers: { 'Authorization' => "Bearer #{owner_token.access_token}" }
      expect(response).to have_http_status(:forbidden)
    end

    it 'updates defaults for owner content creator' do
      allow_any_instance_of(User).to receive(:is_content_creator?).and_return(true)
      patch "/api/v1/leagues/#{league.id}/round_visibility_defaults",
            params: { default_visible_for_new_rounds: false },
            headers: { 'Authorization' => "Bearer #{owner_token.access_token}" }
      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json.dig('data', 'default_visible_for_new_rounds')).to eq(false)
    end
  end

  describe 'POST /api/v1/leagues/:id/rounds/:round_number/publish' do
    it 'requires auth' do
      post "/api/v1/leagues/#{league.id}/rounds/#{round_number}/publish"
      expect(response).to have_http_status(:unauthorized)
    end

    it 'requires owner' do
      allow(member).to receive(:is_content_creator?).and_return(true)
      post "/api/v1/leagues/#{league.id}/rounds/#{round_number}/publish",
           headers: { 'Authorization' => "Bearer #{member_token.access_token}" }
      expect(response).to have_http_status(:unauthorized)
    end

    it 'publishes for owner content creator' do
      allow_any_instance_of(User).to receive(:is_content_creator?).and_return(true)
      post "/api/v1/leagues/#{league.id}/rounds/#{round_number}/publish",
           headers: { 'Authorization' => "Bearer #{owner_token.access_token}" }
      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json.dig('data', 'visible')).to eq(true)
    end
  end

  describe 'enforcement in round scores endpoints' do
    it 'blocks non-owners when default is hidden and round not published, then allows after publish' do
      # Hide by default
      LeagueRoundVisibilitySetting.find_or_create_by!(league:).update!(default_visible_for_new_rounds: false)

      # Non-owner tries to view round scores
      get "/api/v1/youtube/leagues/#{league.id}/rounds/#{round_number}/scores",
          headers: { 'Authorization' => "Bearer #{member_token.access_token}" }
      expect(response).to have_http_status(:forbidden)

      # Publish the round
      allow_any_instance_of(User).to receive(:is_content_creator?).and_return(true)
      post "/api/v1/leagues/#{league.id}/rounds/#{round_number}/publish",
           headers: { 'Authorization' => "Bearer #{owner_token.access_token}" }
      expect(response).to have_http_status(:ok)

      # Now non-owner can view (data presence may vary)
      get "/api/v1/youtube/leagues/#{league.id}/rounds/#{round_number}/scores",
          headers: { 'Authorization' => "Bearer #{member_token.access_token}" }
      expect([200, 404]).to include(response.status)
    end
  end
end
