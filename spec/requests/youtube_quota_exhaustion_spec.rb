require 'rails_helper'

RSpec.describe 'YouTube quota exhaustion error handling', type: :request do
  let(:creator) do
    User.create!(email: '<EMAIL>', username: 'creator', password: 'Password1!', confirmed_at: Time.current,
                 youtube_credentials: {
                   'access_token' => 'tok', 'refresh_token' => 'rtok',
                   'expires_at' => Time.now.to_i + 3600, 'token_type' => 'Bearer',
                   'scope' => YoutubeTokenService::YOUTUBE_SCOPES.join(' ')
                 }.to_json,
                 youtube_channel_id: 'CREATOR_CH')
  end
  let(:user) do
    User.create!(email: '<EMAIL>', username: 'u', password: 'Password1!', confirmed_at: Time.current,
                 youtube_credentials: {
                   'access_token' => 'tok', 'refresh_token' => 'rtok',
                   'expires_at' => Time.now.to_i + 3600, 'token_type' => 'Bearer',
                   'scope' => YoutubeTokenService::YOUTUBE_SCOPES.join(' ')
                 }.to_json)
  end
  let(:token) do
    Devise::Api::Token.create!(resource_owner: user, access_token: 'x' * 64, refresh_token: 'y' * 64, expires_in: 3600)
  end
  let(:headers) { { 'Authorization' => "Bearer #{token.access_token}" } }
  let(:competition) { create(:competition) }
  let(:season) { create(:season, competition:) }

  before do
    allow_any_instance_of(User).to receive(:can_use_youtube_features?).and_return(true)
    allow(YoutubeQuotaService).to receive(:can_make_api_call?).and_call_original
  end

  it 'returns YOUTUBE_QUOTA_EXHAUSTED (429) when joining subscriber league if quota is exhausted' do
    league = League.create!(name: 'SubL', owner: creator, competition_id: competition.id, season_id: season.id,
                            youtube_league: true, subscriber_only: true, youtube_channel_id: 'CHANNEL123')
    allow(YoutubeQuotaService).to receive(:can_make_api_call?).with('subscriptions.list').and_return(false)

    post("/api/v1/leagues/#{league.id}/memberships", headers:)

    expect(response).to have_http_status(:too_many_requests)
    expect(JSON.parse(response.body)['error_key']).to eq('YOUTUBE_QUOTA_EXHAUSTED')
  end

  it 'uses cached subscription result when available even if quota exhausted' do
    league = League.create!(name: 'SubL2', owner: creator, competition_id: competition.id, season_id: season.id,
                            youtube_league: true, subscriber_only: true, youtube_channel_id: 'CHANNEL999')

    # Seed cache as subscribed
    YoutubeCache.write("youtube_subscription_#{user.id}_CHANNEL999", true, expires_in: 1.day)

    allow(YoutubeQuotaService).to receive(:can_make_api_call?).with('subscriptions.list').and_return(false)

    post("/api/v1/leagues/#{league.id}/memberships", headers:)

    expect(response).to have_http_status(:created)
  end
end
