require 'rails_helper'

RSpec.describe 'Membership join limits', type: :request do
  let(:user) do
    User.create!(email: '<EMAIL>', username: 'user', password: 'Password1!', confirmed_at: Time.current)
  end
  let(:token) do
    Devise::Api::Token.create!(resource_owner: user, access_token: 'x' * 64, refresh_token: 'y' * 64, expires_in: 3600)
  end
  let(:headers) { { 'Authorization' => "Bearer #{token.access_token}" } }
  let(:competition) { create(:competition) }
  let(:season) { create(:season, competition:) }

  before do
    AppSetting.create!(key: 'default_league_join_limit', value: '2')
  end

  it 'blocks join when hitting global AppSetting limit' do
    l1 = League.create!(name: 'L1', owner: user, competition_id: competition.id, season_id: season.id,
                        youtube_league: false)
    l2 = League.create!(name: 'L2', owner: user, competition_id: competition.id, season_id: season.id,
                        youtube_league: false)
    post("/api/v1/leagues/#{l1.id}/memberships", headers:)
    post("/api/v1/leagues/#{l2.id}/memberships", headers:)

    l3 = League.create!(name: 'L3', owner: user, competition_id: competition.id, season_id: season.id,
                        youtube_league: false)
    post("/api/v1/leagues/#{l3.id}/memberships", headers:)

    expect(response.status).to eq(422)
    body = JSON.parse(response.body)
    expect(body['error_key']).to eq('MAXIMUM_LEAGUES_REACHED')
    expect(body['limit']).to eq(2)
  end

  it 'respects per-user override' do
    user.update!(league_join_limit: 1)
    l1 = League.create!(name: 'L10', owner: user, competition_id: competition.id, season_id: season.id,
                        youtube_league: false)
    post("/api/v1/leagues/#{l1.id}/memberships", headers:)

    l2 = League.create!(name: 'L11', owner: user, competition_id: competition.id, season_id: season.id,
                        youtube_league: false)
    post("/api/v1/leagues/#{l2.id}/memberships", headers:)
    expect(response.status).to eq(422)
    body = JSON.parse(response.body)
    expect(body['limit']).to eq(1)
  end
end
