require 'rails_helper'

RSpec.describe 'Content Creator Verification Flow', type: :request do
  let(:user) { create(:user) }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:auth_headers) { { 'Authorization' => "Bearer #{token.access_token}" } }

  before do
    # Mock YouTube service to avoid actual API calls
    allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
    allow_any_instance_of(BragRightsYouTubeService).to receive(:get_channel_info).and_return({
                                                                                               id: 'UC1234567890',
                                                                                               title: 'Test Channel',
                                                                                               thumbnail_url: 'https://example.com/thumb.jpg'
                                                                                             })
  end

  describe 'Complete Content Creator Verification Flow' do
    context 'when user goes through the full verification process' do
      it 'successfully verifies as content creator with 100+ subscribers' do
        # Step 1: User starts with no YouTube connection
        expect(user.youtube_connected?).to be false
        expect(user.can_use_youtube_features?).to be false
        expect(user.is_content_creator?).to be false

        # Step 2: User connects YouTube account with sufficient subscribers
        auth_data = {
          channel_id: 'UC1234567890',
          channel_name: 'Test Channel',
          avatar_url: 'https://example.com/thumb.jpg',
          subscriber_count: 150, # Above minimum threshold
          credentials: {
            access_token: 'test_token',
            refresh_token: 'test_refresh',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly',
            expires_in: 3600
          }
        }

        expect(user.connect_youtube_account(auth_data)).to be true

        # Verify connection was successful
        user.reload
        expect(user.youtube_connected?).to be true
        expect(user.youtube_channel_id).to eq('UC1234567890')
        expect(user.youtube_channel_name).to eq('Test Channel')
        expect(user.youtube_subscriber_count).to eq(150)
        expect(user.youtube_verified_at).to be_present

        # Step 3: Check if user can use YouTube features (has proper scope)
        expect(user.can_use_youtube_features?).to be true

        # Step 4: User enables content creator mode
        user.update!(is_content_creator: true)
        expect(user.is_content_creator?).to be true

        # Step 5: Verify user is now a verified YouTube creator
        expect(user.verified_youtube_creator?).to be true

        # Step 6: Check eligibility for creating subscriber-only leagues
        expect(YoutubeEligibilityService.can_create_subscriber_league?(user)).to be true
        expect(YoutubeEligibilityService.subscriber_league_ineligibility_reason(user)).to be_nil
      end

      it 'fails verification when subscriber count is below threshold' do
        # User connects YouTube account with insufficient subscribers
        auth_data = {
          channel_id: 'UC1234567890',
          channel_name: 'Test Channel',
          avatar_url: 'https://example.com/thumb.jpg',
          subscriber_count: 50, # Below minimum threshold
          credentials: {
            access_token: 'test_token',
            refresh_token: 'test_refresh',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly',
            expires_in: 3600
          }
        }

        user.connect_youtube_account(auth_data)
        user.update!(is_content_creator: true)
        user.reload

        # User should not be eligible for subscriber-only leagues
        expect(YoutubeEligibilityService.can_create_subscriber_league?(user)).to be false
        expect(YoutubeEligibilityService.subscriber_league_ineligibility_reason(user))
          .to include('at least 100 subscribers')
      end

      it 'fails verification when user lacks YouTube scope' do
        # User connects with basic OAuth scope only (no YouTube scope)
        auth_data = {
          channel_id: 'UC1234567890',
          channel_name: 'Test Channel',
          avatar_url: 'https://example.com/thumb.jpg',
          subscriber_count: 150,
          credentials: {
            access_token: 'test_token',
            refresh_token: 'test_refresh',
            scope: 'openid email profile', # Missing YouTube scope
            expires_in: 3600
          }
        }

        user.connect_youtube_account(auth_data)
        user.update!(is_content_creator: true)
        user.reload

        # User should not be able to use YouTube features
        expect(user.can_use_youtube_features?).to be false
        expect(YoutubeEligibilityService.can_create_subscriber_league?(user)).to be false
        expect(YoutubeEligibilityService.subscriber_league_ineligibility_reason(user))
          .to include('upgrade your YouTube permissions')
      end
    end

    context 'API endpoint integration' do
      it 'returns correct eligibility status via API' do
        # Set up user with proper YouTube connection and creator status
        auth_data = {
          channel_id: 'UC1234567890',
          channel_name: 'Test Channel',
          subscriber_count: 150,
          credentials: {
            access_token: 'test_token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }
        }
        user.connect_youtube_account(auth_data)
        user.update!(is_content_creator: true)

        # Test the eligibility check endpoint
        get '/api/v1/youtube/check_subscriber_league_eligibility', headers: auth_headers

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['eligible']).to be true
        expect(json_response['subscriber_count']).to eq(150)
        expect(json_response['min_subscribers_required']).to eq(100)
      end
    end
  end
end
