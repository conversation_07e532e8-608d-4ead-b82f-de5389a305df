# spec/support/contract_testing_helper.rb
# Helper module for contract testing with JSON Schema validation

require 'json-schema'

module ContractTestingHelper
  # Validate JSON response against a schema
  def validate_json_schema(response_body, schema)
    parsed_body = JSON.parse(response_body)
    errors = JSON::Validator.fully_validate(schema, parsed_body)
    
    if errors.any?
      raise RSpec::Expectations::ExpectationNotMetError, 
            "JSON Schema validation failed:\n#{errors.join("\n")}\n\nResponse body:\n#{JSON.pretty_generate(parsed_body)}"
    end
    
    parsed_body
  end

  # Validate response status and JSON schema
  def validate_contract_response(response, expected_status, schema)
    expect(response).to have_http_status(expected_status)
    expect(response.content_type).to include('application/json')
    validate_json_schema(response.body, schema)
  end

  # Generate contract test documentation
  def document_contract_test(endpoint, method, description, request_params = {}, response_example = {})
    metadata = {
      contract_test: true,
      endpoint: endpoint,
      method: method,
      description: description,
      request_params: request_params,
      response_example: response_example
    }
    
    # Store contract documentation for later generation
    @contract_documentation ||= []
    @contract_documentation << metadata
  end

  # Validate authentication requirements
  def validate_authentication_required(endpoint, method)
    case method.upcase
    when 'GET'
      get endpoint
    when 'POST'
      post endpoint
    when 'PUT', 'PATCH'
      put endpoint
    when 'DELETE'
      delete endpoint
    end

    expect(response).to have_http_status(:unauthorized)
  end

  # Test progressive OAuth flow requirements
  def validate_progressive_oauth_flow(user, endpoint, method, expected_scope_error: false)
    # Test with user who has no YouTube connection
    user.update!(youtube_credentials: nil)
    
    case method.upcase
    when 'GET'
      get endpoint
    when 'POST'
      post endpoint
    end

    if expected_scope_error
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('needs_scope_upgrade')
    else
      expect(response).to have_http_status(:bad_request)
    end
  end

  # Validate error response format consistency
  def validate_error_response_format(response, expected_status)
    expect(response).to have_http_status(expected_status)
    expect(response.content_type).to include('application/json')
    
    json_response = JSON.parse(response.body)
    expect(json_response).to have_key('error').or have_key('errors')
  end

  # Test rate limiting and security headers
  def validate_security_headers(response)
    # Check for security headers that should be present
    expect(response.headers).to have_key('X-Frame-Options')
    expect(response.headers).to have_key('X-Content-Type-Options')
  end

  # Validate pagination format for list endpoints
  def validate_pagination_format(response_body)
    parsed_body = JSON.parse(response_body)
    
    if parsed_body.is_a?(Hash) && parsed_body.key?('data') && parsed_body['data'].is_a?(Array)
      # Check for pagination metadata
      expect(parsed_body).to have_key('meta') if parsed_body['data'].length >= 10
    end
  end

  # Generate OpenAPI specification from contract tests
  def generate_openapi_spec
    return unless @contract_documentation

    spec = {
      openapi: '3.0.0',
      info: {
        title: 'Brag Rights YouTube OAuth API',
        version: '1.0.0',
        description: 'API for YouTube OAuth integration and subscriber-only leagues'
      },
      servers: [
        {
          url: 'http://localhost:3000/api/v1',
          description: 'Development server'
        }
      ],
      paths: {}
    }

    @contract_documentation.each do |doc|
      path_key = doc[:endpoint]
      method_key = doc[:method].downcase

      spec[:paths][path_key] ||= {}
      spec[:paths][path_key][method_key] = {
        summary: doc[:description],
        parameters: doc[:request_params],
        responses: {
          '200' => {
            description: 'Success',
            content: {
              'application/json' => {
                example: doc[:response_example]
              }
            }
          }
        }
      }
    end

    spec
  end
end

# Include the helper in RSpec
RSpec.configure do |config|
  config.include ContractTestingHelper, type: :request
end
