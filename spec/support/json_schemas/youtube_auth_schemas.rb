# spec/support/json_schemas/youtube_auth_schemas.rb
# JSON Schema definitions for YouTube OAuth API contracts

module JsonSchemas
  module YoutubeAuth
    # Schema for YouTube connection status response
    STATUS_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[
        connected is_content_creator has_basic_scope has_youtube_scope needs_scope_upgrade can_use_youtube_features
      ],
      properties: {
        connected: { type: 'boolean' },
        is_content_creator: { type: 'boolean' },
        channel_id: { type: %w[string null] },
        channel_name: { type: %w[string null] },
        avatar_url: { type: %w[string null] },
        subscriber_count: { type: %w[integer null] },
        verified_at: { type: %w[string null] },
        has_basic_scope: { type: 'boolean' },
        has_youtube_scope: { type: 'boolean' },
        needs_scope_upgrade: { type: 'boolean' },
        can_use_youtube_features: { type: 'boolean' },
        quota_status: {
          type: 'object',
          required: %w[status can_authenticate can_join_league features_available],
          properties: {
            status: { type: 'string' },
            can_authenticate: { type: 'boolean' },
            can_join_league: { type: 'boolean' },
            features_available: { type: 'boolean' }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false
    }.freeze

    # Schema for OAuth URL generation response
    SCOPE_UPGRADE_URL_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[oauth_url state scopes],
      properties: {
        oauth_url: {
          type: 'string',
          pattern: '^https://accounts\.google\.com/o/oauth2/v2/auth\?'
        },
        state: {
          type: 'string',
          minLength: 32,
          maxLength: 32
        },
        scopes: {
          type: 'array',
          items: { type: 'string' },
          minItems: 1
        }
      },
      additionalProperties: false
    }.freeze

    # Schema for successful OAuth scope upgrade response
    SCOPE_UPGRADE_SUCCESS_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[success message scopes],
      properties: {
        success: { type: 'boolean', enum: [true] },
        message: { type: 'string' },
        scopes: {
          type: 'array',
          items: { type: 'string' },
          minItems: 1
        }
      },
      additionalProperties: false
    }.freeze

    # Schema for YouTube connection success response
    CONNECT_SUCCESS_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[success message user_data],
      properties: {
        success: { type: 'boolean', enum: [true] },
        message: { type: 'string' },
        user_data: {
          type: 'object',
          required: %w[youtube_connected youtube_channel_id youtube_channel_name],
          properties: {
            youtube_connected: { type: 'boolean', enum: [true] },
            youtube_channel_id: { type: 'string', minLength: 1 },
            youtube_channel_name: { type: 'string', minLength: 1 },
            youtube_avatar_url: { type: %w[string null] },
            youtube_subscriber_count: { type: %w[integer null] },
            is_content_creator: { type: 'boolean' },
            verified_youtube_creator: { type: 'boolean' }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false
    }.freeze

    # Schema for subscription verification response
    SUBSCRIPTION_VERIFICATION_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[status connected subscribed],
      properties: {
        status: { type: 'string', enum: %w[success error] },
        connected: { type: 'boolean' },
        subscribed: { type: 'boolean' },
        message: { type: %w[string null] },
        needs_scope_upgrade: { type: %w[boolean null] }
      },
      additionalProperties: false
    }.freeze

    # Schema for error responses
    ERROR_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[error],
      properties: {
        error: { type: 'string', minLength: 1 }
      },
      additionalProperties: false
    }.freeze

    # Schema for validation error responses
    VALIDATION_ERROR_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[errors],
      properties: {
        errors: {
          oneOf: [
            { type: 'string' },
            {
              type: 'array',
              items: { type: 'string' },
              minItems: 1
            }
          ]
        }
      },
      additionalProperties: true # Allow additional fields like min_subscribers_required
    }.freeze
  end
end
