# spec/support/json_schemas/youtube_controller_schemas.rb
# JSON Schema definitions for YouTube controller API contracts

module JsonSchemas
  module YoutubeController
    # Schema for subscription status response
    SUBSCRIPTION_STATUS_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[status connected subscribed],
      properties: {
        status: { type: 'string', enum: %w[success error] },
        connected: { type: 'boolean' },
        subscribed: { type: 'boolean' },
        message: { type: %w[string null] },
        needs_scope_upgrade: { type: %w[boolean null] },
        channel: {
          type: %w[object null],
          properties: {
            id: { type: 'string' }
          },
          additionalProperties: false
        },
        error_key: { type: %w[string null] }
      },
      additionalProperties: false
    }.freeze

    # Schema for creator status update success response
    CREATOR_STATUS_UPDATE_SUCCESS_SCHEMA = {
      type: 'object',
      required: %w[is_content_creator],
      properties: {
        message: { type: 'string' },
        message_key: { type: 'string' },
        is_content_creator: { type: %w[boolean null] }
      },
      additionalProperties: false
    }.freeze

    # Schema for subscriber league eligibility check response
    SUBSCRIBER_LEAGUE_ELIGIBILITY_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[eligible min_subscribers_required],
      properties: {
        eligible: { type: 'boolean' },
        subscriber_count: { type: %w[integer null] },
        min_subscribers_required: { type: 'integer', minimum: 1 },
        reason: { type: %w[string null] },
        message: { type: %w[string null] }
      },
      additionalProperties: false
    }.freeze

    # Schema for error responses requiring scope upgrade
    SCOPE_UPGRADE_ERROR_RESPONSE_SCHEMA = {
      type: 'object',
      required: %w[needs_scope_upgrade],
      properties: {
        error: { type: 'string', minLength: 1 },
        error_key: { type: 'string' },
        needs_scope_upgrade: { type: 'boolean', enum: [true] }
      },
      additionalProperties: false
    }.freeze
  end
end
