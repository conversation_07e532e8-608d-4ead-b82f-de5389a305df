require 'rails_helper'

RSpec.describe YoutubeTokenService, 'Progressive OAuth' do
  include YoutubeTokenServiceTestHelper

  let(:user) { YoutubeTokenServiceTestHelper::MockUser.new }

  describe '.has_basic_scope?' do
    context 'when user has no YouTube credentials' do
      it 'returns false' do
        expect(YoutubeTokenService.has_basic_scope?(user)).to be false
      end
    end

    context 'when user has basic OAuth scope' do
      before do
        credentials = {
          'access_token' => 'token',
          'scope' => 'openid email profile'
        }
        user.update(youtube_credentials: credentials.to_json)
      end

      it 'returns true' do
        expect(YoutubeTokenService.has_basic_scope?(user)).to be true
      end
    end

    context 'when user has partial basic scope' do
      before do
        credentials = {
          'access_token' => 'token',
          'scope' => 'openid email'
        }
        user.update(youtube_credentials: credentials.to_json)
      end

      it 'returns false' do
        expect(YoutubeTokenService.has_basic_scope?(user)).to be false
      end
    end
  end

  describe '.has_youtube_scope?' do
    context 'when user has no YouTube credentials' do
      it 'returns false' do
        expect(YoutubeTokenService.has_youtube_scope?(user)).to be false
      end
    end

    context 'when user has YouTube scope' do
      before do
        credentials = {
          'access_token' => 'token',
          'scope' => 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
        }
        user.update(youtube_credentials: credentials.to_json)
      end

      it 'returns true' do
        expect(YoutubeTokenService.has_youtube_scope?(user)).to be true
      end
    end

    context 'when user has only basic scope' do
      before do
        credentials = {
          'access_token' => 'token',
          'scope' => 'openid email profile'
        }
        user.update(youtube_credentials: credentials.to_json)
      end

      it 'returns false' do
        expect(YoutubeTokenService.has_youtube_scope?(user)).to be false
      end
    end
  end

  describe '.generate_oauth_url' do
    it 'generates correct OAuth URL with basic scopes' do
      url = YoutubeTokenService.generate_oauth_url(
        'client_id',
        'http://localhost:3000/callback',
        YoutubeTokenService::BASIC_SCOPES
      )

      expect(url).to include('accounts.google.com/o/oauth2/v2/auth')
      expect(url).to include('client_id=client_id')
      expect(url).to include('redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fcallback')
      expect(url).to include('scope=openid+email+profile')
      expect(url).to include('response_type=code')
      expect(url).to include('access_type=offline')
      expect(url).to include('prompt=consent')
    end

    it 'generates correct OAuth URL with all scopes' do
      url = YoutubeTokenService.generate_oauth_url(
        'client_id',
        'http://localhost:3000/callback',
        YoutubeTokenService::ALL_SCOPES
      )

      expect(url).to include('scope=openid+email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fyoutube.readonly')
    end

    it 'includes state parameter when provided' do
      url = YoutubeTokenService.generate_oauth_url(
        'client_id',
        'http://localhost:3000/callback',
        YoutubeTokenService::BASIC_SCOPES,
        'test_state'
      )

      expect(url).to include('state=test_state')
    end
  end

  describe '.upgrade_user_scope' do
    let(:new_credentials) do
      {
        'access_token' => 'new_token',
        'refresh_token' => 'new_refresh',
        'scope' => 'openid email profile https://www.googleapis.com/auth/youtube.readonly',
        'expires_in' => 3600
      }
    end

    it 'upgrades user scope successfully' do
      expect(YoutubeTokenService).to receive(:store_credentials).with(user, new_credentials).and_return(true)

      result = YoutubeTokenService.upgrade_user_scope(user, new_credentials)
      expect(result).to be true
    end

    it 'returns false when credential storage fails' do
      expect(YoutubeTokenService).to receive(:store_credentials).with(user, new_credentials).and_return(false)

      result = YoutubeTokenService.upgrade_user_scope(user, new_credentials)
      expect(result).to be false
    end
  end
end
