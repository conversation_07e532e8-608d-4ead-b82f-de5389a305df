require 'rails_helper'
require 'support/json_schemas/youtube_auth_schemas'
require 'support/contract_testing_helper'

RSpec.describe 'YouTube Auth API Contract', type: :request do
  let(:user) { create(:user) }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:auth_headers) { { 'Authorization' => "Bearer #{token.access_token}" } }

  before do
    # Enable YouTube features for testing
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:[]).with('YOUTUBE_CONNECT_ENABLED').and_return('true')
    allow(ENV).to receive(:[]).with('GOOGLE_CLIENT_ID').and_return('test_client_id')
    allow(ENV).to receive(:[]).with('GOOGLE_CLIENT_SECRET').and_return('test_client_secret')
    allow(ENV).to receive(:[]).with('YOUTUBE_OAUTH_CALLBACK_URL').and_return('http://localhost:3000/auth/callback')
  end

  describe 'GET /api/v1/youtube_auth/status' do
    context 'when user is authenticated' do
      it 'returns YouTube connection status with progressive OAuth information' do
        document_contract_test(
          '/api/v1/youtube_auth/status',
          'GET',
          'Get YouTube connection status with progressive OAuth scope information'
        )

        get '/api/v1/youtube_auth/status', headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeAuth::STATUS_RESPONSE_SCHEMA
        )

        # Validate business logic
        expect(json_response['connected']).to be_in([true, false])
        expect(json_response['has_basic_scope']).to be_in([true, false])
        expect(json_response['has_youtube_scope']).to be_in([true, false])
        expect(json_response['can_use_youtube_features']).to be_in([true, false])
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube_auth/status', 'GET')
      end
    end
  end

  describe 'GET /api/v1/youtube_auth/scope_upgrade_url' do
    context 'with valid parameters' do
      let(:valid_params) do
        {
          redirect_uri: 'http://localhost:3000/auth/callback'
        }
      end

      it 'returns OAuth URL for scope upgrade' do
        document_contract_test(
          '/api/v1/youtube_auth/scope_upgrade_url',
          'GET',
          'Generate OAuth URL for progressive scope upgrade',
          valid_params
        )

        get '/api/v1/youtube_auth/scope_upgrade_url', params: valid_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeAuth::SCOPE_UPGRADE_URL_RESPONSE_SCHEMA
        )

        # Validate OAuth URL format
        expect(json_response['oauth_url']).to include('accounts.google.com')
        expect(json_response['oauth_url']).to include('scope=')
        expect(json_response['scopes']).to include('openid')
        expect(json_response['scopes']).to include('https://www.googleapis.com/auth/youtube.readonly')
      end
    end

    context 'with missing redirect_uri' do
      it 'returns validation error' do
        get '/api/v1/youtube_auth/scope_upgrade_url', headers: auth_headers

        validate_contract_response(
          response,
          :bad_request,
          JsonSchemas::YoutubeAuth::ERROR_RESPONSE_SCHEMA
        )
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube_auth/scope_upgrade_url', 'GET')
      end
    end
  end

  describe 'POST /api/v1/youtube_auth/upgrade_scope' do
    let(:valid_upgrade_params) do
      {
        code: 'test_auth_code',
        redirect_uri: 'http://localhost:3000/auth/callback',
        state: 'test_state_12345678901234567890123456789012'
      }
    end

    before do
      # Mock the state verification
      allow(Rails.cache).to receive(:read).with("oauth_state_#{user.id}").and_return('test_state_12345678901234567890123456789012')
      allow(Rails.cache).to receive(:delete).with("oauth_state_#{user.id}")
    end

    context 'with valid parameters and successful token exchange' do
      before do
        # Mock successful token exchange
        allow_any_instance_of(Api::V1::YoutubeAuthController).to receive(:exchange_code_for_tokens).and_return({
                                                                                                                 'access_token' => 'new_access_token',
                                                                                                                 'refresh_token' => 'new_refresh_token',
                                                                                                                 'scope' => 'openid email profile https://www.googleapis.com/auth/youtube.readonly',
                                                                                                                 'expires_in' => 3600
                                                                                                               })

        # Mock the YoutubeTokenService.upgrade_user_scope method
        allow(YoutubeTokenService).to receive(:upgrade_user_scope).and_return(true)
      end

      it 'successfully upgrades OAuth scope' do
        document_contract_test(
          '/api/v1/youtube_auth/upgrade_scope',
          'POST',
          'Upgrade user OAuth scope to include YouTube permissions',
          valid_upgrade_params
        )

        post '/api/v1/youtube_auth/upgrade_scope', params: valid_upgrade_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeAuth::SCOPE_UPGRADE_SUCCESS_RESPONSE_SCHEMA
        )

        expect(json_response['success']).to be true
        expect(json_response['scopes']).to include('https://www.googleapis.com/auth/youtube.readonly')
      end
    end

    context 'with missing required parameters' do
      it 'returns validation error for missing code' do
        invalid_params = valid_upgrade_params.except(:code)
        post '/api/v1/youtube_auth/upgrade_scope', params: invalid_params, headers: auth_headers

        validate_contract_response(
          response,
          :bad_request,
          JsonSchemas::YoutubeAuth::ERROR_RESPONSE_SCHEMA
        )
      end

      it 'returns validation error for missing state' do
        invalid_params = valid_upgrade_params.except(:state)
        post '/api/v1/youtube_auth/upgrade_scope', params: invalid_params, headers: auth_headers

        validate_contract_response(
          response,
          :bad_request,
          JsonSchemas::YoutubeAuth::ERROR_RESPONSE_SCHEMA
        )
      end
    end

    context 'with invalid state parameter' do
      before do
        allow(Rails.cache).to receive(:read).with("oauth_state_#{user.id}").and_return('different_state')
      end

      it 'returns validation error' do
        post '/api/v1/youtube_auth/upgrade_scope', params: valid_upgrade_params, headers: auth_headers

        validate_contract_response(
          response,
          :bad_request,
          JsonSchemas::YoutubeAuth::ERROR_RESPONSE_SCHEMA
        )
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube_auth/upgrade_scope', 'POST')
      end
    end
  end

  describe 'POST /api/v1/youtube_auth/verify_subscription' do
    let(:subscription_params) { { channel_id: 'UC1234567890' } }

    context 'when user has YouTube scope and is subscribed' do
      before do
        # Set up user with YouTube connection and scope
        user.update!(
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }.to_json,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current
        )

        # Mock subscription verification
        allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
      end

      it 'returns subscription status' do
        document_contract_test(
          '/api/v1/youtube_auth/verify_subscription',
          'POST',
          'Verify if user is subscribed to a YouTube channel',
          subscription_params
        )

        post '/api/v1/youtube_auth/verify_subscription', params: subscription_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeAuth::SUBSCRIPTION_VERIFICATION_RESPONSE_SCHEMA
        )

        expect(json_response['status']).to eq('success')
        expect(json_response['connected']).to be true
        expect(json_response['subscribed']).to be true
      end
    end

    context 'when user needs scope upgrade' do
      before do
        # Set up user with basic OAuth scope only
        user.update!(
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile'
          }.to_json,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current
        )
      end

      it 'returns scope upgrade requirement' do
        post '/api/v1/youtube_auth/verify_subscription', params: subscription_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeAuth::SUBSCRIPTION_VERIFICATION_RESPONSE_SCHEMA
        )

        expect(json_response['status']).to eq('error')
        expect(json_response['needs_scope_upgrade']).to be true
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube_auth/verify_subscription', 'POST')
      end
    end
  end
end
