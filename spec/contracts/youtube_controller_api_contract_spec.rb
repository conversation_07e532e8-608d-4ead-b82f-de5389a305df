require 'rails_helper'
require 'support/json_schemas/youtube_controller_schemas'
require 'support/contract_testing_helper'

RSpec.describe 'YouTube Controller API Contract', type: :request do
  let(:user) { create(:user) }
  let(:token) { create(:devise_api_token, resource_owner: user) }
  let(:auth_headers) { { 'Authorization' => "Bearer #{token.access_token}" } }

  before do
    # Enable YouTube features for testing
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:[]).with('YOUTUBE_CONNECT_ENABLED').and_return('true')
    allow(ENV).to receive(:[]).with('YOUTUBE_CREATOR_ENABLED').and_return('true')
    allow(ENV).to receive(:[]).with('GOOGLE_CLIENT_ID').and_return('test_client_id')
    allow(ENV).to receive(:[]).with('GOOGLE_CLIENT_SECRET').and_return('test_client_secret')
    allow(ENV).to receive(:[]).with('YOUTUBE_OAUTH_CALLBACK_URL').and_return('http://localhost:3000/auth/callback')
  end

  describe 'GET /api/v1/youtube/subscription_status' do
    let(:channel_params) { { channel_id: 'UC1234567890' } }

    context 'when user has YouTube scope and is subscribed' do
      before do
        user.update!(
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }.to_json,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current
        )

        allow_any_instance_of(BragRightsYouTubeService).to receive(:verify_subscription?).and_return(true)
      end

      it 'returns subscription status' do
        document_contract_test(
          '/api/v1/youtube/subscription_status',
          'GET',
          'Check if user is subscribed to a specific YouTube channel',
          channel_params
        )

        get '/api/v1/youtube/subscription_status', params: channel_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::SUBSCRIPTION_STATUS_RESPONSE_SCHEMA
        )

        expect(json_response['status']).to eq('success')
        expect(json_response['connected']).to be true
        expect(json_response['subscribed']).to be true
      end
    end

    context 'when user needs scope upgrade' do
      before do
        user.update!(
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile'
          }.to_json,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current
        )
      end

      it 'returns scope upgrade requirement' do
        get '/api/v1/youtube/subscription_status', params: channel_params, headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::SUBSCRIPTION_STATUS_RESPONSE_SCHEMA
        )

        expect(json_response['status']).to eq('error')
        expect(json_response['needs_scope_upgrade']).to be true
      end
    end

    context 'with missing channel_id parameter' do
      it 'returns validation error' do
        get '/api/v1/youtube/subscription_status', headers: auth_headers

        # accept error_key format
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to have_key('error_key')
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube/subscription_status', 'GET')
      end
    end
  end

  describe 'POST /api/v1/youtube/update_creator_status' do
    context 'when user has YouTube connection and scope' do
      before do
        user.update!(
          youtube_channel_id: 'UC1234567890',
          youtube_channel_name: 'Test Channel',
          youtube_subscriber_count: 150,
          youtube_verified_at: Time.current,
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }.to_json
        )
      end

      it 'successfully updates creator status' do
        document_contract_test(
          '/api/v1/youtube/update_creator_status',
          'POST',
          'Enable content creator mode for verified YouTube users'
        )

        post '/api/v1/youtube/update_creator_status', headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::CREATOR_STATUS_UPDATE_SUCCESS_SCHEMA
        )

        expect(json_response).to include('message_key').or include('message')
        expect(json_response['is_content_creator']).to be_in([true, false, nil])
      end
    end

    context 'when user needs scope upgrade' do
      before do
        user.update!(
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile'
          }.to_json,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current
        )
      end

      it 'returns scope upgrade requirement' do
        post '/api/v1/youtube/update_creator_status', headers: auth_headers

        validate_contract_response(
          response,
          :bad_request,
          JsonSchemas::YoutubeController::SCOPE_UPGRADE_ERROR_RESPONSE_SCHEMA
        )
      end
    end

    context 'when user has no YouTube connection' do
      it 'returns connection error' do
        post '/api/v1/youtube/update_creator_status', headers: auth_headers

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to have_key('error_key')
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube/update_creator_status', 'POST')
      end
    end
  end

  describe 'GET /api/v1/youtube/check_subscriber_league_eligibility' do
    context 'when user is eligible for subscriber leagues' do
      before do
        user.update!(
          is_content_creator: true,
          youtube_channel_id: 'UC1234567890',
          youtube_subscriber_count: 150,
          youtube_verified_at: Time.current,
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }.to_json
        )
      end

      it 'returns eligibility status' do
        document_contract_test(
          '/api/v1/youtube/check_subscriber_league_eligibility',
          'GET',
          'Check if user is eligible to create subscriber-only leagues'
        )

        get '/api/v1/youtube/check_subscriber_league_eligibility', headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::SUBSCRIBER_LEAGUE_ELIGIBILITY_RESPONSE_SCHEMA
        )

        expect(json_response['eligible']).to be true
        expect(json_response['subscriber_count']).to eq(150)
        expect(json_response['min_subscribers_required']).to eq(100)
      end
    end

    context 'when user is not eligible (insufficient subscribers)' do
      before do
        user.update!(
          is_content_creator: true,
          youtube_channel_id: 'UC1234567890',
          youtube_subscriber_count: 50,
          youtube_verified_at: Time.current,
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
          }.to_json
        )
      end

      it 'returns ineligibility with reason' do
        get '/api/v1/youtube/check_subscriber_league_eligibility', headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::SUBSCRIBER_LEAGUE_ELIGIBILITY_RESPONSE_SCHEMA
        )

        expect(json_response['eligible']).to be false
        expect(json_response['message']).to include('at least 100 subscribers')
      end
    end

    context 'when user needs scope upgrade' do
      before do
        user.update!(
          is_content_creator: true,
          youtube_channel_id: 'UC1234567890',
          youtube_verified_at: Time.current,
          youtube_credentials: {
            access_token: 'token',
            scope: 'openid email profile'
          }.to_json
        )
      end

      it 'returns ineligibility with scope upgrade reason' do
        get '/api/v1/youtube/check_subscriber_league_eligibility', headers: auth_headers

        json_response = validate_contract_response(
          response,
          :ok,
          JsonSchemas::YoutubeController::SUBSCRIBER_LEAGUE_ELIGIBILITY_RESPONSE_SCHEMA
        )

        expect(json_response['eligible']).to be false
        expect(json_response['message']).to include('upgrade your YouTube permissions')
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        validate_authentication_required('/api/v1/youtube/check_subscriber_league_eligibility', 'GET')
      end
    end
  end

  describe 'Progressive OAuth Flow Integration' do
    it 'validates complete progressive OAuth user journey' do
      # Step 1: User starts with no YouTube connection
      get '/api/v1/youtube_auth/status', headers: auth_headers
      json_response = JSON.parse(response.body)
      expect(json_response['connected']).to be false
      expect(json_response['can_use_youtube_features']).to be false

      # Step 2: User gets OAuth URL for scope upgrade
      get '/api/v1/youtube_auth/scope_upgrade_url',
          params: { redirect_uri: 'http://localhost:3000/auth/callback' },
          headers: auth_headers
      expect(response).to have_http_status(:ok)

      # Step 3: User connects with YouTube scope
      user.update!(
        youtube_channel_id: 'UC1234567890',
        youtube_verified_at: Time.current,
        youtube_credentials: {
          access_token: 'token',
          scope: 'openid email profile https://www.googleapis.com/auth/youtube.readonly'
        }.to_json
      )

      # Step 4: Verify user can now use YouTube features
      get '/api/v1/youtube_auth/status', headers: auth_headers
      json_response = JSON.parse(response.body)
      expect(json_response['connected']).to be true
      expect(json_response['can_use_youtube_features']).to be true

      # Step 5: User can now check subscription status
      get '/api/v1/youtube/subscription_status',
          params: { channel_id: 'UC1234567890' },
          headers: auth_headers
      expect(response).to have_http_status(:ok)
    end
  end
end
