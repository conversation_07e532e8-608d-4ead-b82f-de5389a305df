require 'rails_helper'

RSpec.describe LeagueRoundVisibility, type: :model do
  let(:area) { create(:area) }
  let(:competition) { create(:competition, area: area) }
  let(:season) { create(:season, competition: competition) }
  let(:user) { create(:user) }
  let(:league) { create(:league, competition: competition, season: season, owner: user) }

  describe 'validations' do
    it 'validates presence of round_number' do
      lrv = LeagueRoundVisibility.new(league: league, season: season)
      expect(lrv).not_to be_valid
      expect(lrv.errors[:round_number]).to include("can't be blank")
    end

    it 'validates round_number is a positive integer' do
      lrv = LeagueRoundVisibility.new(league: league, season: season, round_number: 0)
      expect(lrv).not_to be_valid
      expect(lrv.errors[:round_number]).to include('must be greater than 0')
    end

    it 'validates visible is boolean' do
      lrv = LeagueRoundVisibility.new(league: league, season: season, round_number: 1, visible: 'invalid')
      expect(lrv).not_to be_valid
    end

    it 'requires made_visible_by_user when visible is true' do
      lrv = LeagueRoundVisibility.new(league: league, season: season, round_number: 1, visible: true)
      expect(lrv).not_to be_valid
      expect(lrv.errors[:made_visible_by_user]).to include("can't be blank")
    end

    it 'requires visible_at when visible is true' do
      lrv = LeagueRoundVisibility.new(league: league, season: season, round_number: 1, visible: true, made_visible_by_user: user)
      expect(lrv).not_to be_valid
      expect(lrv.errors[:visible_at]).to include("can't be blank")
    end
  end

  describe '#can_unpublish?' do
    let(:lrv) { create(:league_round_visibility, league: league, season: season, round_number: 1) }

    context 'when round is not visible' do
      before { lrv.update!(visible: false, visible_at: nil, made_visible_by_user: nil) }

      it 'returns true' do
        expect(lrv.can_unpublish?).to be true
      end
    end

    context 'when round is visible' do
      before do
        lrv.update!(visible: true, visible_at: Time.current, made_visible_by_user: user)
      end

      context 'within grace period (less than 1 day)' do
        it 'returns true' do
          expect(lrv.can_unpublish?).to be true
        end
      end

      context 'exactly at grace period boundary (1 day)' do
        before { lrv.update!(visible_at: 1.day.ago) }

        it 'returns true' do
          expect(lrv.can_unpublish?).to be true
        end
      end

      context 'after grace period (more than 1 day)' do
        before { lrv.update!(visible_at: 25.hours.ago) }

        it 'returns false' do
          expect(lrv.can_unpublish?).to be false
        end
      end
    end

    context 'when visible_at is nil but visible is true' do
      before do
        lrv.update_columns(visible: true, visible_at: nil, made_visible_by_user_id: user.id)
      end

      it 'returns true' do
        expect(lrv.can_unpublish?).to be true
      end
    end
  end

  describe 'unpublishing validation' do
    let(:lrv) { create(:league_round_visibility, league: league, season: season, round_number: 1) }

    before do
      lrv.update!(visible: true, visible_at: Time.current, made_visible_by_user: user)
    end

    context 'within grace period' do
      it 'allows unpublishing' do
        lrv.visible = false
        expect(lrv).to be_valid
        expect(lrv.save).to be true
      end
    end

    context 'after grace period' do
      before { lrv.update_columns(visible_at: 25.hours.ago) }

      it 'prevents unpublishing' do
        lrv.visible = false
        expect(lrv).not_to be_valid
        expect(lrv.errors[:visible]).to include('cannot be unpublished after the 1-day grace period has expired')
      end
    end

    context 'when not changing visibility' do
      it 'allows other updates' do
        lrv.round_number = 2
        expect(lrv).to be_valid
        expect(lrv.save).to be true
      end
    end

    context 'when changing from false to true' do
      before { lrv.update!(visible: false, visible_at: nil, made_visible_by_user: nil) }

      it 'allows publishing' do
        lrv.visible = true
        lrv.visible_at = Time.current
        lrv.made_visible_by_user = user
        expect(lrv).to be_valid
        expect(lrv.save).to be true
      end
    end
  end

  describe 'associations' do
    let(:lrv) { create(:league_round_visibility, league: league, season: season, round_number: 1) }

    it 'belongs to league' do
      expect(lrv.league).to eq(league)
    end

    it 'belongs to season' do
      expect(lrv.season).to eq(season)
    end

    it 'can have showcased users' do
      user1 = create(:user)
      user2 = create(:user)
      
      lrv.showcases.create!(user: user1)
      lrv.showcases.create!(user: user2)
      
      expect(lrv.showcased_users).to contain_exactly(user1, user2)
    end
  end
end
