# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LeagueCustomization, type: :model do
  let(:league) { create(:league) }

  it 'is created by default for a new league' do
    expect(league.league_customization).to be_present
    expect(league.league_customization).to be_valid
  end

  it 'validates color hex format' do
    lc = described_class.new(league:, primary: 'not-a-color')
    expect(lc).not_to be_valid
    expect(lc.errors[:primary].join).to match(/valid color/i)
  end

  it 'validates header placement enum' do
    lc = described_class.new(league:, header_placement: 'somewhere')
    expect(lc).not_to be_valid
    expect(lc.errors[:header_placement]).to be_present
  end

  it 'validates logo url format' do
    lc = described_class.new(league:, logo_url: 'not-a-url')
    expect(lc).not_to be_valid
    expect(lc.errors[:logo_url]).to be_present
  end

  it 'enforces uniqueness per league' do
    expect(league.league_customization).to be_present
    expect do
      described_class.create!(league:)
    end.to raise_error(ActiveRecord::RecordInvalid)
  end
end
