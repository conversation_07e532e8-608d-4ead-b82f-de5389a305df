require 'rails_helper'

RSpec.describe User, 'Progressive OAuth' do
  let(:user) { create(:user) }

  describe '#has_basic_oauth_scope?' do
    it 'delegates to YoutubeTokenService' do
      expect(YoutubeTokenService).to receive(:has_basic_scope?).with(user).and_return(true)
      expect(user.has_basic_oauth_scope?).to be true
    end
  end

  describe '#has_youtube_oauth_scope?' do
    it 'delegates to YoutubeTokenService' do
      expect(YoutubeTokenService).to receive(:has_youtube_scope?).with(user).and_return(true)
      expect(user.has_youtube_oauth_scope?).to be true
    end
  end

  describe '#needs_youtube_scope_upgrade?' do
    context 'when user is not connected to YouTube' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(false)
      end

      it 'returns false' do
        expect(user.needs_youtube_scope_upgrade?).to be false
      end
    end

    context 'when user is connected but has no YouTube scope' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(true)
        allow(user).to receive(:has_youtube_oauth_scope?).and_return(false)
      end

      it 'returns true' do
        expect(user.needs_youtube_scope_upgrade?).to be true
      end
    end

    context 'when user is connected and has YouTube scope' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(true)
        allow(user).to receive(:has_youtube_oauth_scope?).and_return(true)
      end

      it 'returns false' do
        expect(user.needs_youtube_scope_upgrade?).to be false
      end
    end
  end

  describe '#can_use_youtube_features?' do
    context 'when user is not connected to YouTube' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(false)
      end

      it 'returns false' do
        expect(user.can_use_youtube_features?).to be false
      end
    end

    context 'when user is connected but has no YouTube scope' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(true)
        allow(user).to receive(:has_youtube_oauth_scope?).and_return(false)
      end

      it 'returns false' do
        expect(user.can_use_youtube_features?).to be false
      end
    end

    context 'when user is connected and has YouTube scope' do
      before do
        allow(user).to receive(:youtube_connected?).and_return(true)
        allow(user).to receive(:has_youtube_oauth_scope?).and_return(true)
      end

      it 'returns true' do
        expect(user.can_use_youtube_features?).to be true
      end
    end
  end
end
